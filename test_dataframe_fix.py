#!/usr/bin/env python3
"""
Test DataFrame display in table tab
"""

import pandas as pd
import numpy as np

# Test 1: Create a simple DataFrame
print("🧪 Testing DataFrame Display")
data = {
    'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    'Age': [25, 30, 35, 28, 32],
    'City': ['New York', 'London', 'Tokyo', 'Paris', 'Berlin'],
    'Salary': [50000, 60000, 70000, 55000, 65000],
    'Department': ['Engineering', 'Marketing', 'Sales', 'HR', 'Engineering']
}

df = pd.DataFrame(data)
print("Created DataFrame:")
print(df)

# Test 2: Assign to result variable (should show in table tab)
print("\n📊 Assigning DataFrame to result variable")
result = df
print("DataFrame assigned to 'result' variable")

# This should trigger table view
result

# Test 3: Create another DataFrame
print("\n📈 Creating another DataFrame")
sales_data = {
    'Month': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    'Sales': [1000, 1200, 1100, 1300, 1400],
    'Profit': [200, 240, 220, 260, 280]
}

sales_df = pd.DataFrame(sales_data)
print("Sales DataFrame:")
print(sales_df)

# Test 4: Return the sales DataFrame
print("\n💰 Returning sales DataFrame")
sales_df
