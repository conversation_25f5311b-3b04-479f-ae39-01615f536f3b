"use client";

import { useState, useMemo, useEffect } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Dialog } from "../ui/dialog";
import { motion } from "framer-motion";
import { TableCell } from './TableComponents/TableCell';
import { TableControls } from './TableComponents/TableControls';
import { TableDialogs } from './TableComponents/TableDialogs';
import { TablePagination } from './TableComponents/TablePagination';
import { ColumnBarChart } from './TableComponents/ColumnBarChart';
import { format, parseISO } from "date-fns";
import { Button } from "../ui/button";
import { Save } from "lucide-react";

interface TableData {
  [key: string]: string | number | boolean;
}

interface EditHistoryItem {
  type: 'edit' | 'deleteRow' | 'deleteColumn';
  row: number | null;
  column: string | null;
  oldValue: any | null;
  newValue: any | null;
  timestamp: Date;
}

interface Version {
  id: string;
  versionNumber: number;
  changes: EditHistoryItem[];
  createdAt: Date;
  user: {
    name: string;
  };
}

interface TableTabProps {
  data: TableData[];
  headers: string[];
  datasetId?: string;
  onMaximize?: () => void;
  onSave?: (updatedData: TableData[]) => void;
  isLoadingDataset?: boolean;
}

const formatDate = (dateValue: any) => {
  try {
    if (!dateValue) return 'N/A';
    if (typeof dateValue === 'string') {
      return format(parseISO(dateValue), 'MMM d, yyyy HH:mm');
    }
    if (typeof dateValue === 'number') {
      return format(new Date(dateValue), 'MMM d, yyyy HH:mm');
    }
    if (dateValue instanceof Date) {
      return format(dateValue, 'MMM d, yyyy HH:mm');
    }
    return 'Invalid date';
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

// Enhanced utility function to calculate optimal column width based on content
const calculateColumnWidth = (data: TableData[], accessor: string, headerText: string) => {
  if (!data || data.length === 0) return 100;

  // Sample first 100 rows for better accuracy
  const sampleData = data.slice(0, 100);

  // Get all values for this column
  const columnValues = sampleData.map(row => {
    const value = row[accessor];
    if (value === null || value === undefined) return '';
    return String(value);
  });

  // Calculate optimal width using enhanced algorithm
  const allTexts = [headerText, ...columnValues];
  const maxLength = Math.max(...allTexts.map(text => text.length));

  // Smart width calculation based on content type and length
  let baseWidth;
  if (maxLength <= 10) {
    baseWidth = 80;  // Very short content
  } else if (maxLength <= 20) {
    baseWidth = 120; // Short content
  } else if (maxLength <= 40) {
    baseWidth = 180; // Medium content
  } else if (maxLength <= 80) {
    baseWidth = 240; // Long content
  } else {
    baseWidth = 300; // Very long content
  }

  // Fine-tune based on actual character width estimation
  const estimatedPixelWidth = maxLength * 7.5 + 24; // 7.5px per char + padding
  const finalWidth = Math.max(80, Math.min(350, Math.max(baseWidth, estimatedPixelWidth)));

  return Math.round(finalWidth);
};

export function TableTab<TData extends TableData>({ data: initialData, headers, datasetId, onSave, isLoadingDataset = false }: TableTabProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState("");
  const [rowSelection, setRowSelection] = useState({});
  const [editHistory, setEditHistory] = useState<Array<EditHistoryItem>>([]);
  const [undoHistory, setUndoHistory] = useState<Array<EditHistoryItem>>([]);
  const [redoHistory, setRedoHistory] = useState<Array<EditHistoryItem>>([]);
  const [data, setData] = useState(initialData);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showChangesPreview, setShowChangesPreview] = useState(false);
  const [showAddColumnDialog, setShowAddColumnDialog] = useState(false);
  const [newColumnName, setNewColumnName] = useState("");
  const [versions, setVersions] = useState<Version[]>([]);
  const [showVersionHistory, setShowVersionHistory] = useState(false);

  useEffect(() => {
    fetchVersionHistory();
  }, [datasetId]);

  const fetchVersionHistory = async () => {
    if (!datasetId) return;
    try {
      const response = await fetch(`/api/datasets?datasetId=${datasetId}`);
      const result = await response.json();
      if (result.success) {
        setVersions(result.versions || []);
      } else {
        setVersions([]);
      }
    } catch (error) {
      console.error('Error fetching version history:', error);
      setVersions([]);
    }
  };

  const handleAddRow = () => {
    const newRow: TableData = {};
    headers.forEach(header => {
      newRow[header] = "";
    });
    setData([...data, newRow]);
    toast.success("New row added");
  };

  const handleAddColumn = () => {
    if (!newColumnName) return;
    const updatedHeaders = [...headers, newColumnName];
    const updatedData = data.map(row => ({
      ...row,
      [newColumnName]: ""
    }));
    setColumnVisibility(prev => ({
      ...prev,
      [newColumnName]: true
    }));
    setData(updatedData);
    headers.push(newColumnName);
    setShowAddColumnDialog(false);
    setNewColumnName("");
    toast.success("New column added");
  };

  const handleSaveChanges = async () => {
    if (!datasetId) {
      toast.error('Dataset ID is required');
      return;
    }
    if (editHistory.length === 0) {
      toast.error('No changes to save');
      return;
    }
    try {
      const savePromise = fetch('/api/datasets', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          datasetId,
          changes: editHistory,
          newData: data,
          headers,
        })
      }).then(async (response) => {
        const result = await response.json();
        if (!result.success) {
          throw new Error(result.error || 'Failed to save changes');
        }
        return result;
      });
      await toast.promise(savePromise, {
        loading: 'Saving changes...',
        success: () => {
          setEditHistory([]);
          fetchVersionHistory();
          return 'Changes saved successfully';
        },
        error: 'Failed to save changes'
      });
      localStorage.setItem(`dataset-${datasetId}`, JSON.stringify({
        data,
        headers,
        lastSaved: new Date().toISOString()
      }));
      if (onSave) {
        onSave(data);
      }
    } catch (error) {
      console.error('Error saving changes:', error);
    }
  };

  const handleUndo = () => {
    if (editHistory.length === 0) return;

    const lastEdit = editHistory[editHistory.length - 1];
    setEditHistory(prev => prev.slice(0, -1));
    setRedoHistory(prev => [...prev, lastEdit]);

    // Revert the change
    if (lastEdit.type === 'edit' && lastEdit.row !== null && lastEdit.column) {
      const updatedData = [...data];
      updatedData[lastEdit.row] = {
        ...updatedData[lastEdit.row],
        [lastEdit.column]: lastEdit.oldValue
      };
      setData(updatedData);
      toast.success('Change undone');
    }
  };

  const handleRedo = () => {
    if (redoHistory.length === 0) return;

    const lastRedo = redoHistory[redoHistory.length - 1];
    setRedoHistory(prev => prev.slice(0, -1));
    setEditHistory(prev => [...prev, lastRedo]);

    // Reapply the change
    if (lastRedo.type === 'edit' && lastRedo.row !== null && lastRedo.column) {
      const updatedData = [...data];
      updatedData[lastRedo.row] = {
        ...updatedData[lastRedo.row],
        [lastRedo.column]: lastRedo.newValue
      };
      setData(updatedData);
      toast.success('Change redone');
    }
  };

  const handleCellEdit = (rowIndex: number, column: string, oldValue: any, newValue: any) => {
    const change: EditHistoryItem = {
      type: 'edit',
      row: rowIndex,
      column,
      oldValue,
      newValue,
      timestamp: new Date()
    };

    setEditHistory(prev => [...prev, change]);
    setRedoHistory([]); // Clear redo history on new edit

    const updatedData = [...data];
    updatedData[rowIndex] = {
      ...updatedData[rowIndex],
      [column]: newValue
    };

    setData(updatedData);
    toast.success('Value updated', { duration: 2000 });
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'z') {
        if (e.shiftKey) {
          handleRedo();
        } else {
          handleUndo();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [editHistory, redoHistory]);

  const renderChangeContent = (change: EditHistoryItem) => {
    switch (change.type) {
      case 'deleteRow':
        return change.row !== null ? (
          <div className="text-red-500">
            Deleted row {change.row + 1}
          </div>
        ) : null;
      case 'deleteColumn':
        return change.column ? (
          <div className="text-red-500">
            Deleted column "{change.column}"
          </div>
        ) : null;
      case 'edit':
        return change.row !== null && change.column ? (
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">
              {`Row ${change.row + 1}, ${change.column}:`}
            </span>
            <span className="line-through text-red-500">
              {change.oldValue?.toString() || 'empty'}
            </span>
            <span className="text-muted-foreground">→</span>
            <span className="text-green-500">
              {change.newValue?.toString() || 'empty'}
            </span>
          </div>
        ) : null;
      default:
        return null;
    }
  };

  // Calculate dynamic column widths
  const columnWidths = useMemo(() => {
    const widths: Record<string, number> = {};
    headers.forEach(header => {
      widths[header] = calculateColumnWidth(data, header, header);
    });
    return widths;
  }, [headers, data]);

  const table = useReactTable<TData>({
    data: data as TData[],
    columns: useMemo(() => {
      return headers.map((header) => {
        const columnWidth = columnWidths[header] || 120;
        return {
          id: String(header),
          accessorKey: String(header),
          size: columnWidth,
          header: ({ column }) => (
            <div className="flex flex-col items-start gap-1 w-full">
              <div className="w-full px-1 pt-1">
                <ColumnBarChart
                  data={data}
                  accessor={String(header)}
                  width={Math.min(columnWidth - 16, 120)}
                  height={18}
                />
              </div>
              <div className="flex items-center gap-1 w-full">
                <button
                  className="text-xs data-[state=sorted]:text-primary font-semibold text-left whitespace-nowrap overflow-hidden text-ellipsis"
                  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                  title={String(header)}
                  style={{ maxWidth: `${columnWidth - 16}px` }}
                >
                  {String(header)}
                </button>
              </div>
            </div>
          ),
          cell: ({ row, column }) => {
            const value = row.getValue(String(header));
            return (
              <TableCell
                row={row}
                column={column}
                value={value}
                columnWidth={columnWidth}
                onEdit={(newValue) => handleCellEdit(row.index, String(header), value, newValue)}
                onDeleteRow={(index) => {
                  const updatedData = [...data];
                  updatedData.splice(index, 1);
                  setData(updatedData);
                  toast.success('Row deleted');
                }}
                onDuplicateRow={(index) => {
                  const newRow = { ...row.original };
                  const updatedData = [...data];
                  updatedData.splice(index + 1, 0, newRow);
                  setData(updatedData);
                  toast.success('Row duplicated');
                }}
              />
            );
          },
        };
      });
    }, [headers, data, columnWidths]),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
  });

  const renderTableContent = () => (
    <div className="relative w-full h-full overflow-hidden">
      <div
        className={cn(
          "overflow-auto",
          "scrollbar-thin scrollbar-thumb-muted-foreground/30 scrollbar-track-muted/10",
          "transition-colors duration-200",
          "h-full w-full"
        )}
        style={{
          height: 'calc(100vh - 12rem)' // Adjusted for fixed header/footer
        }}
      >
        {/* Dynamic Spreadsheet Table with Horizontal Scroll */}
        <div className="min-w-max">
          <table className="border-collapse text-sm" style={{ tableLayout: 'auto', width: 'max-content' }}>
          {/* Enhanced Header */}
          <thead className="sticky top-0 z-30 bg-gradient-to-b from-muted/80 to-muted/60 backdrop-blur-sm">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id} className="border-b-2 border-border/30">
                {headerGroup.headers.map((header, index) => {
                  const columnWidth = header.getSize();
                  return (
                    <th
                      key={header.id}
                      className={cn(
                        "px-2 py-2 text-left align-top font-semibold text-xs",
                        "bg-gradient-to-b from-muted/90 to-muted/70",
                        "border-r border-border/40 last:border-r-0",
                        "hover:bg-accent/50 transition-all duration-200",
                        "group select-none cursor-pointer",
                        "shadow-sm"
                      )}
                      style={{
                        width: `${columnWidth}px`,
                        minWidth: `${columnWidth}px`,
                        maxWidth: `${columnWidth}px`
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>

          {/* Enhanced Body with Dynamic Sizing */}
          <tbody className="bg-background">
            {table.getRowModel().rows.map((row, i) => (
              <tr
                key={row.id}
                className={cn(
                  "relative group",
                  i % 2 === 0 ? "bg-background" : "bg-muted/3",
                  "hover:bg-primary/5 transition-all duration-150",
                  "border-b border-border/20"
                )}
              >
                {row.getVisibleCells().map((cell, cellIndex) => {
                  const columnWidth = cell.column.getSize();
                  return (
                    <td
                      key={cell.id}
                      className={cn(
                        "p-0 border-r border-border/20 last:border-r-0",
                        "relative group-hover:bg-primary/3 transition-colors",
                        "focus-within:bg-primary/10 focus-within:ring-1 focus-within:ring-primary/30",
                        "overflow-visible"
                      )}
                      style={{
                        height: '32px',
                        width: `${columnWidth}px`,
                        minWidth: `${columnWidth}px`,
                        maxWidth: `${columnWidth}px`
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  if (isLoadingDataset) {
    return (
      <div className="w-full h-full flex flex-col gap-2 p-4 animate-pulse">
        <div className="flex gap-2 mb-2">
          <div className="h-6 w-32 bg-muted rounded" />
          <div className="h-6 w-32 bg-muted rounded" />
          <div className="h-6 w-32 bg-muted rounded" />
          <div className="h-6 w-32 bg-muted rounded" />
        </div>
        {[...Array(8)].map((_, i) => (
          <div key={i} className="flex gap-2 mb-1">
            <div className="h-5 w-32 bg-muted rounded" />
            <div className="h-5 w-32 bg-muted rounded" />
            <div className="h-5 w-32 bg-muted rounded" />
            <div className="h-5 w-32 bg-muted rounded" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full w-full max-w-full overflow-hidden">
      {/* Fixed Header Controls */}
      <div className="sticky top-0 z-50 shadow-sm bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0 border-b">
        <TableControls
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          handleAddRow={handleAddRow}
          handleAddColumn={handleAddColumn}
          setShowVersionHistory={setShowVersionHistory}
          versions={versions}
          editHistory={editHistory}
          setShowChangesPreview={setShowChangesPreview}
          handleSaveChanges={handleSaveChanges}
          rowOperation={{ type: '', loading: false }}
          onUndo={handleUndo}
          onRedo={handleRedo}
          canUndo={editHistory.length > 0}
          canRedo={redoHistory.length > 0}
        />
      </div>

      {/* Spreadsheet Table Container */}
      <div className="flex-1 w-full bg-background overflow-hidden relative">
        {renderTableContent()}
      </div>

      {/* Fixed Footer Pagination */}
      <div className="sticky bottom-0 z-40 border-t shadow-sm bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
        <TablePagination table={table} />
      </div>

      {/* Floating Save Button - Emergency Fallback */}
      {editHistory.length > 0 && (
        <div className="fixed bottom-6 right-6 z-50 lg:hidden">
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              onClick={handleSaveChanges}
              className="h-14 w-14 rounded-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg border-2 border-background"
            >
              <Save className="h-6 w-6" />
            </Button>
          </motion.div>
          <div className="absolute -top-2 -left-2 h-6 w-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
            {editHistory.length}
          </div>
        </div>
      )}

      <TableDialogs
        showAddColumnDialog={showAddColumnDialog}
        setShowAddColumnDialog={setShowAddColumnDialog}
        newColumnName={newColumnName}
        setNewColumnName={setNewColumnName}
        handleAddColumn={handleAddColumn}
        showVersionHistory={showVersionHistory}
        setShowVersionHistory={setShowVersionHistory}
        versions={versions}
        formatDate={formatDate}
        renderChangeContent={renderChangeContent}
        showChangesPreview={showChangesPreview}
        setShowChangesPreview={setShowChangesPreview}
        editHistory={editHistory}
      />
    </div>
  );
}
