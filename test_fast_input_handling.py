#!/usr/bin/env python3
"""
Test script for fast input handling
This should demonstrate immediate response to user input
"""

def test_simple_input():
    """Test basic input functionality"""
    name = input("Enter your name: ")
    print(f"Hello, {name}!")
    return name

def test_multiple_inputs():
    """Test multiple sequential inputs"""
    print("Let's collect some information...")
    
    name = input("What's your name? ")
    age = input("How old are you? ")
    city = input("Which city are you from? ")
    
    try:
        age_num = int(age)
        print(f"\nSummary:")
        print(f"Name: {name}")
        print(f"Age: {age_num}")
        print(f"City: {city}")
        print(f"Next year you'll be {age_num + 1} years old!")
    except ValueError:
        print(f"Invalid age: {age}")
        print("Please enter a number for age next time.")

def test_conditional_input():
    """Test input with conditional logic"""
    answer = input("Do you want to continue? (y/n): ")
    
    if answer.lower() in ['y', 'yes']:
        name = input("Great! What's your name? ")
        print(f"Nice to meet you, {name}!")
    else:
        print("Okay, maybe next time!")

# Run the test
if __name__ == "__main__":
    print("=== Fast Input Handling Test ===")
    print("This test should respond immediately to your input.")
    print()
    
    # Test simple input
    result = test_simple_input()
    print(f"Received: {result}")
    print()
    
    # Test multiple inputs
    test_multiple_inputs()
    print()
    
    # Test conditional input
    test_conditional_input()
    
    print("\n=== Test Complete ===")
