# Plotly 3D Examples for HRatlas
# These examples demonstrate various 3D plotting capabilities

import plotly.graph_objects as go
import plotly.express as px
import numpy as np
import pandas as pd

# Example 1: Simple 3D Scatter Plot
def create_3d_scatter():
    """Create a simple 3D scatter plot"""
    # Generate sample data
    np.random.seed(42)
    n = 100
    x = np.random.randn(n)
    y = np.random.randn(n)
    z = np.random.randn(n)
    colors = np.random.randn(n)
    
    fig = go.Figure(data=go.Scatter3d(
        x=x, y=y, z=z,
        mode='markers',
        marker=dict(
            size=8,
            color=colors,
            colorscale='Viridis',
            showscale=True
        ),
        text=[f'Point {i}' for i in range(n)],
        hovertemplate='<b>Point %{text}</b><br>X: %{x}<br>Y: %{y}<br>Z: %{z}<extra></extra>'
    ))
    
    fig.update_layout(
        title='3D Scatter Plot Example',
        scene=dict(
            xaxis_title='X Axis',
            yaxis_title='Y Axis',
            zaxis_title='Z Axis'
        ),
        width=700,
        height=500
    )
    
    fig.show()

# Example 2: 3D Surface Plot
def create_3d_surface():
    """Create a 3D surface plot"""
    # Generate surface data
    x = np.linspace(-5, 5, 50)
    y = np.linspace(-5, 5, 50)
    X, Y = np.meshgrid(x, y)
    Z = np.sin(np.sqrt(X**2 + Y**2))
    
    fig = go.Figure(data=go.Surface(
        x=X, y=Y, z=Z,
        colorscale='Plasma',
        showscale=True
    ))
    
    fig.update_layout(
        title='3D Surface Plot - Sine Wave',
        scene=dict(
            xaxis_title='X',
            yaxis_title='Y',
            zaxis_title='Z = sin(√(x² + y²))'
        ),
        width=700,
        height=500
    )
    
    fig.show()

# Example 3: 3D Line Plot
def create_3d_line():
    """Create a 3D line plot"""
    t = np.linspace(0, 4*np.pi, 100)
    x = np.cos(t)
    y = np.sin(t)
    z = t
    
    fig = go.Figure(data=go.Scatter3d(
        x=x, y=y, z=z,
        mode='lines+markers',
        line=dict(
            color='red',
            width=6
        ),
        marker=dict(
            size=4,
            color='blue'
        )
    ))
    
    fig.update_layout(
        title='3D Helix - Parametric Line',
        scene=dict(
            xaxis_title='X = cos(t)',
            yaxis_title='Y = sin(t)',
            zaxis_title='Z = t'
        ),
        width=700,
        height=500
    )
    
    fig.show()

# Example 4: 3D Bar Chart
def create_3d_bar():
    """Create a 3D bar chart"""
    # Sample data
    data = {
        'x': [1, 2, 3, 4, 5] * 5,
        'y': [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5],
        'z': np.random.randint(1, 20, 25)
    }
    
    fig = go.Figure(data=go.Scatter3d(
        x=data['x'],
        y=data['y'],
        z=[0] * len(data['x']),  # Base at z=0
        mode='markers',
        marker=dict(
            size=data['z'],
            color=data['z'],
            colorscale='Blues',
            showscale=True,
            sizemode='diameter',
            sizeref=2.*max(data['z'])/(40.**2),
            sizemin=4
        ),
        text=[f'Value: {z}' for z in data['z']],
        hovertemplate='X: %{x}<br>Y: %{y}<br>Value: %{text}<extra></extra>'
    ))
    
    fig.update_layout(
        title='3D Bubble Chart',
        scene=dict(
            xaxis_title='X Category',
            yaxis_title='Y Category',
            zaxis_title='Height'
        ),
        width=700,
        height=500
    )
    
    fig.show()

# Example 5: Multiple 3D Objects
def create_multiple_3d():
    """Create multiple 3D objects in one plot"""
    # Sphere
    u = np.linspace(0, 2 * np.pi, 50)
    v = np.linspace(0, np.pi, 50)
    x_sphere = np.outer(np.cos(u), np.sin(v))
    y_sphere = np.outer(np.sin(u), np.sin(v))
    z_sphere = np.outer(np.ones(np.size(u)), np.cos(v))
    
    # Cone
    theta = np.linspace(0, 2*np.pi, 30)
    r = np.linspace(0, 1, 20)
    R, T = np.meshgrid(r, theta)
    x_cone = R * np.cos(T) + 3
    y_cone = R * np.sin(T)
    z_cone = R + 1
    
    fig = go.Figure()
    
    # Add sphere
    fig.add_trace(go.Surface(
        x=x_sphere, y=y_sphere, z=z_sphere,
        colorscale='Blues',
        name='Sphere',
        showscale=False
    ))
    
    # Add cone
    fig.add_trace(go.Surface(
        x=x_cone, y=y_cone, z=z_cone,
        colorscale='Reds',
        name='Cone',
        showscale=False
    ))
    
    fig.update_layout(
        title='Multiple 3D Objects',
        scene=dict(
            xaxis_title='X',
            yaxis_title='Y',
            zaxis_title='Z'
        ),
        width=700,
        height=500
    )
    
    fig.show()

# Test function to run all examples
def run_all_examples():
    """Run all 3D plotting examples"""
    print("🎯 Running Plotly 3D Examples...")
    
    print("1. Creating 3D Scatter Plot...")
    create_3d_scatter()
    
    print("2. Creating 3D Surface Plot...")
    create_3d_surface()
    
    print("3. Creating 3D Line Plot...")
    create_3d_line()
    
    print("4. Creating 3D Bar Chart...")
    create_3d_bar()
    
    print("5. Creating Multiple 3D Objects...")
    create_multiple_3d()
    
    print("✅ All examples completed!")

# Simple test - uncomment to run
# create_3d_scatter()
