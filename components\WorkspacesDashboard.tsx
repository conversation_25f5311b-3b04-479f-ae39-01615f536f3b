'use client'

import React, { useEffect, useState, useCallback } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { useAuth } from '@clerk/nextjs'
import { toast } from 'sonner'
import {
  Loader2,
  Plus,
  Database,
  BarChart3,
  Globe,
  Lock,
  Calendar,
  Users,
  ArrowRight,
  FolderPlus,
  FileSpreadsheet,
  Upload,
  BookOpen,
  Activity,
  Server,
  Cpu,
  MemoryStick,
  HardDrive,
  AlertCircle,
  CheckCircle,
  Zap,
  TrendingUp
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import { WorkspaceModal } from '@/components/ChartBuilder/WorkspaceModal'
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, Area, AreaChart, Toolt<PERSON> } from 'recharts'
import dynamic from 'next/dynamic'

interface DataWorkspace {
  id: string
  name: string
  description?: string
  isPublic: boolean
  publicId?: string
  notebooks: Array<{
    id: string
    name: string
    _count?: { cells: number }
  }>
  dashboards: Array<{
    id: string
    name: string
    _count?: { items: number }
  }>
  createdAt: string
  updatedAt: string
}

interface Dataset {
  id: string
  name: string
  description?: string
  fileType: string
  createdAt: string
  headers: string[]
  rowCount: number
  isEmbedded?: boolean
  embeddingModel?: string
}

interface ServerStatus {
  status: string
  python_version: string
  server_uptime: number
  cpu_usage: number
  memory: {
    total: number
    available: number
    percent: number
    used: number
  }
  disk: {
    total: number
    free: number
    used: number
    percent: number
  }
  process: {
    pid: number
    memory_info: any
    cpu_percent: number
    create_time: number
  }
  kernel: {
    execution_count: number
    namespace_variables: number
  }
}

interface MetricDataPoint {
  time: string
  cpu: number
  memory: number
  timestamp: number
}

export default function WorkspacesDashboard() {
  const router = useRouter()
  const { isLoaded, isSignedIn } = useAuth()
  const [workspaces, setWorkspaces] = useState<DataWorkspace[]>([])
  const [datasets, setDatasets] = useState<Dataset[]>([])
  const [serverStatus, setServerStatus] = useState<ServerStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [metricsHistory, setMetricsHistory] = useState<MetricDataPoint[]>([])
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'warning' | 'disconnected'>('disconnected')

  // Real-time monitoring updates
  const updateMetricsHistory = useCallback((newStatus: ServerStatus) => {
    const now = new Date()
    const timeString = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })

    const newDataPoint: MetricDataPoint = {
      time: timeString,
      cpu: newStatus.cpu_usage,
      memory: newStatus.memory.percent,
      timestamp: now.getTime()
    }

    setMetricsHistory(prev => {
      const updated = [...prev, newDataPoint]
      // Keep only last 20 data points for smooth animation
      return updated.slice(-20)
    })

    // Determine connection status based on metrics
    if (newStatus.status === 'running') {
      if (newStatus.cpu_usage > 80 || newStatus.memory.percent > 85) {
        setConnectionStatus('warning')
      } else {
        setConnectionStatus('connected')
      }
    } else {
      setConnectionStatus('disconnected')
    }
  }, [])

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in')
      return
    }

    if (isLoaded && isSignedIn) {
      fetchData()

      // Set up real-time monitoring updates every 3 seconds
      const interval = setInterval(() => {
        fetchServerStatus()
      }, 3000)

      return () => clearInterval(interval)
    }
  }, [isLoaded, isSignedIn])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Fetch workspaces and datasets
      const [workspacesRes, datasetsRes] = await Promise.allSettled([
        fetch('/api/workspaces'),
        fetch('/api/datasets')
      ])

      // Handle workspaces
      if (workspacesRes.status === 'fulfilled' && workspacesRes.value.ok) {
        const workspacesData = await workspacesRes.value.json()
        if (workspacesData.success) {
          setWorkspaces(workspacesData.workspaces)
        }
      }

      // Handle datasets
      if (datasetsRes.status === 'fulfilled' && datasetsRes.value.ok) {
        const datasetsData = await datasetsRes.value.json()
        if (datasetsData.success) {
          setDatasets(datasetsData.datasets)
        }
      }

      // Fetch initial server status
      await fetchServerStatus()

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      toast.error('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const fetchServerStatus = async () => {
    try {
      const response = await fetch('/api/monitoring')
      if (response.ok) {
        const serverData = await response.json()
        setServerStatus(serverData)
        updateMetricsHistory(serverData)
      } else {
        setConnectionStatus('disconnected')
      }
    } catch (error) {
      console.error('Error fetching server status:', error)
      setConnectionStatus('disconnected')
    }
  }

  const handleWorkspaceCreated = (newWorkspace: DataWorkspace) => {
    setWorkspaces(prev => [newWorkspace, ...prev])
    toast.success('Data workspace created successfully!')
    router.push(`/hr/chartbuilder?workspace=${newWorkspace.id}`)
  }

  const openWorkspace = (workspaceId: string) => {
    router.push(`/hr/chartbuilder?workspace=${workspaceId}`)
  }

  const createNotebook = (workspaceId?: string) => {
    const url = workspaceId 
      ? `/hr/chartbuilder/notebook/new?workspace=${workspaceId}`
      : '/hr/chartbuilder/notebook/new'
    router.push(url)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusColor = (status: 'connected' | 'warning' | 'disconnected') => {
    switch (status) {
      case 'connected':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200'
      case 'warning':
        return 'text-amber-600 bg-amber-50 border-amber-200'
      case 'disconnected':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusIcon = (status: 'connected' | 'warning' | 'disconnected') => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4" />
      case 'warning':
        return <AlertCircle className="h-4 w-4" />
      case 'disconnected':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Server className="h-4 w-4" />
    }
  }

  if (!isLoaded || loading) {
    return (
      <div className="space-y-6">
        <div className="text-center space-y-1">
          <Skeleton className="h-6 w-48 mx-auto" />
          <Skeleton className="h-3 w-72 mx-auto" />
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="rounded-xl">
              <CardContent className="p-3">
                <Skeleton className="h-12 w-full rounded-lg" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="rounded-xl">
              <CardHeader className="pb-2 p-3">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-2 w-1/2" />
              </CardHeader>
              <CardContent className="p-3 pt-0">
                <Skeleton className="h-16 w-full rounded-lg" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Compact Header */}
      {/* <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">
          Analytics Workspace
        </h2>
        <p className="text-sm text-muted-foreground max-w-xl mx-auto">
          Manage workspaces, datasets, and monitor your analytics environment
        </p>
      </div> */}

      {/* Compact Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <Card className="rounded-xl hover:shadow-sm transition-all duration-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xl font-bold">{workspaces.length}</p>
                <p className="text-xs text-muted-foreground">Workspaces</p>
              </div>
              <div className="p-2 bg-primary/10 rounded-lg">
                <Database className="h-4 w-4 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="rounded-xl hover:shadow-sm transition-all duration-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xl font-bold">
                  {workspaces.reduce((acc, ws) => acc + ws.notebooks.length, 0)}
                </p>
                <p className="text-xs text-muted-foreground">Notebooks</p>
              </div>
              <div className="p-2 bg-emerald-500/10 rounded-lg">
                <BookOpen className="h-4 w-4 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="rounded-xl hover:shadow-sm transition-all duration-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xl font-bold">{datasets.length}</p>
                <p className="text-xs text-muted-foreground">Datasets</p>
              </div>
              <div className="p-2 bg-violet-500/10 rounded-lg">
                <FileSpreadsheet className="h-4 w-4 text-violet-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="rounded-xl hover:shadow-sm transition-all duration-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-xs font-medium border ${getStatusColor(connectionStatus)}`}>
                  {getStatusIcon(connectionStatus)}
                  {connectionStatus === 'connected' ? 'Online' :
                   connectionStatus === 'warning' ? 'Load' : 'Offline'}
                </div>
                <p className="text-xs text-muted-foreground mt-1">Server</p>
              </div>
              <div className="p-2 bg-muted rounded-lg">
                <Server className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Curved Quick Actions Layout */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {/* Main curved card - spans 2 columns on mobile */}
        <div className="col-span-2 md:col-span-2">
          <Card
            className="border border-dashed hover:border-primary/50 hover:bg-accent/30 transition-all duration-200 cursor-pointer group h-full"
            style={{
              borderRadius: '1rem 1rem 1rem 3rem'
            }}
            onClick={() => setShowCreateModal(true)}
          >
            <CardContent className="flex items-center justify-between p-4 h-full">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors">
                  <FolderPlus className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Create New Workspace</h3>
                  <p className="text-xs text-muted-foreground">Start organizing your data projects</p>
                </div>
              </div>
              <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
            </CardContent>
          </Card>
        </div>

        {/* Complementary cards */}
        <Card
          className="border border-dashed hover:border-emerald-500/50 hover:bg-emerald-500/5 transition-all duration-200 cursor-pointer group rounded-xl"
          onClick={() => createNotebook()}
        >
          <CardContent className="flex flex-col items-center justify-center p-3 text-center h-full">
            <div className="p-2 bg-emerald-500/10 rounded-lg mb-2 group-hover:bg-emerald-500/20 transition-colors">
              <BookOpen className="h-4 w-4 text-emerald-600" />
            </div>
            <h3 className="font-medium text-xs">Notebook</h3>
            <p className="text-xs text-muted-foreground">Quick start</p>
          </CardContent>
        </Card>

        <Card
          className="border border-dashed hover:border-violet-500/50 hover:bg-violet-500/5 transition-all duration-200 cursor-pointer group rounded-xl"
          onClick={() => router.push('/hr/dataeditor')}
        >
          <CardContent className="flex flex-col items-center justify-center p-3 text-center h-full">
            <div className="p-2 bg-violet-500/10 rounded-lg mb-2 group-hover:bg-violet-500/20 transition-colors">
              <Upload className="h-4 w-4 text-violet-600" />
            </div>
            <h3 className="font-medium text-xs">Import</h3>
            <p className="text-xs text-muted-foreground">Add data</p>
          </CardContent>
        </Card>
      </div>

      {/* Secondary action row */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <Card
          className="border border-dashed hover:border-muted-foreground/50 hover:bg-muted/30 transition-all duration-200 cursor-pointer group rounded-xl"
          onClick={() => router.push('/hr/chartbuilder/workspaces')}
        >
          <CardContent className="flex items-center gap-2 p-3">
            <div className="p-2 bg-muted rounded-lg group-hover:bg-muted/80 transition-colors">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <h3 className="font-medium text-xs">Browse All</h3>
              <p className="text-xs text-muted-foreground">View workspaces</p>
            </div>
          </CardContent>
        </Card>

        <Card
          className="border border-dashed hover:border-blue-500/50 hover:bg-blue-500/5 transition-all duration-200 cursor-pointer group rounded-xl"
          onClick={() => router.push('/hr/monitoring')}
        >
          <CardContent className="flex items-center gap-2 p-3">
            <div className="p-2 bg-blue-500/10 rounded-lg group-hover:bg-blue-500/20 transition-colors">
              <Activity className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-xs">Monitor</h3>
              <p className="text-xs text-muted-foreground">Server status</p>
            </div>
          </CardContent>
        </Card>

        <Card
          className="border border-dashed hover:border-orange-500/50 hover:bg-orange-500/5 transition-all duration-200 cursor-pointer group rounded-xl"
          onClick={() => router.push('/hr/dataeditor')}
        >
          <CardContent className="flex items-center gap-2 p-3">
            <div className="p-2 bg-orange-500/10 rounded-lg group-hover:bg-orange-500/20 transition-colors">
              <FileSpreadsheet className="h-4 w-4 text-orange-600" />
            </div>
            <div>
              <h3 className="font-medium text-xs">Datasets</h3>
              <p className="text-xs text-muted-foreground">Manage data</p>
            </div>
          </CardContent>
        </Card>

        <Card
          className="border border-dashed hover:border-purple-500/50 hover:bg-purple-500/5 transition-all duration-200 cursor-pointer group rounded-xl"
          onClick={() => router.push('/hr/chartbuilder')}
        >
          <CardContent className="flex items-center gap-2 p-3">
            <div className="p-2 bg-purple-500/10 rounded-lg group-hover:bg-purple-500/20 transition-colors">
              <Zap className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <h3 className="font-medium text-xs">Analytics</h3>
              <p className="text-xs text-muted-foreground">Build charts</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workspaces Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Recent Workspaces</h3>
          {workspaces.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              className="rounded-lg h-8 text-xs"
              onClick={() => router.push('/hr/chartbuilder/workspaces')}
            >
              View All <ArrowRight className="h-3 w-3 ml-1" />
            </Button>
          )}
        </div>

        {workspaces.length === 0 ? (
          // Compact Empty State for New Users
          <Card className="border border-dashed rounded-xl">
            <CardContent className="flex flex-col items-center justify-center p-8 text-center">
              <div className="relative mb-4">
                <div className="p-4 bg-primary/10 rounded-2xl">
                  <Database className="h-8 w-8 text-primary" />
                </div>
                <div className="absolute -top-1 -right-1 p-1 bg-emerald-500/10 rounded-full">
                  <Plus className="h-3 w-3 text-emerald-600" />
                </div>
              </div>
              <h3 className="text-lg font-semibold mb-2">Welcome to Analytics!</h3>
              <p className="text-muted-foreground mb-6 max-w-sm text-sm">
                Create your first workspace to organize data analysis projects and build dashboards.
              </p>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  onClick={() => setShowCreateModal(true)}
                  className="rounded-lg"
                  size="sm"
                >
                  <FolderPlus className="h-3 w-3 mr-2" />
                  Create Workspace
                </Button>
                <Button
                  variant="outline"
                  onClick={() => createNotebook()}
                  className="rounded-lg"
                  size="sm"
                >
                  <BookOpen className="h-3 w-3 mr-2" />
                  Start Notebook
                </Button>
              </div>
              <div className="mt-6 grid grid-cols-3 gap-3 w-full max-w-xs">
                <div className="text-center p-2 bg-muted/50 rounded-lg">
                  <Database className="h-4 w-4 text-primary mx-auto mb-1" />
                  <p className="text-xs font-medium">Organize</p>
                </div>
                <div className="text-center p-2 bg-muted/50 rounded-lg">
                  <BookOpen className="h-4 w-4 text-emerald-600 mx-auto mb-1" />
                  <p className="text-xs font-medium">Analyze</p>
                </div>
                <div className="text-center p-2 bg-muted/50 rounded-lg">
                  <BarChart3 className="h-4 w-4 text-violet-600 mx-auto mb-1" />
                  <p className="text-xs font-medium">Visualize</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {workspaces.slice(0, 3).map((workspace) => (
              <Card key={workspace.id} className="rounded-xl hover:shadow-sm transition-all duration-200 group">
                <CardHeader className="pb-2 p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-primary/10 rounded-lg">
                        <Database className="h-3 w-3 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-sm font-semibold line-clamp-1">
                          {workspace.name}
                        </CardTitle>
                        <div className="flex items-center gap-1">
                          {workspace.isPublic ? (
                            <Globe className="h-2 w-2 text-primary" />
                          ) : (
                            <Lock className="h-2 w-2 text-muted-foreground" />
                          )}
                          <span className="text-xs text-muted-foreground">
                            {workspace.isPublic ? 'Public' : 'Private'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  {workspace.description && (
                    <CardDescription className="line-clamp-1 text-xs mt-1">
                      {workspace.description}
                    </CardDescription>
                  )}
                </CardHeader>

                <CardContent className="space-y-3 p-3 pt-0">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-center p-2 bg-muted/50 rounded-lg">
                      <p className="text-sm font-semibold">{workspace.notebooks.length}</p>
                      <p className="text-xs text-muted-foreground">Notebooks</p>
                    </div>
                    <div className="text-center p-2 bg-muted/50 rounded-lg">
                      <p className="text-sm font-semibold">{workspace.dashboards.length}</p>
                      <p className="text-xs text-muted-foreground">Dashboards</p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      onClick={() => openWorkspace(workspace.id)}
                      className="flex-1 rounded-lg h-7 text-xs"
                      size="sm"
                    >
                      Open <ArrowRight className="h-2 w-2 ml-1" />
                    </Button>
                    <Button
                      onClick={() => createNotebook(workspace.id)}
                      variant="outline"
                      size="sm"
                      className="rounded-lg h-7 w-7 p-0"
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Datasets Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Recent Datasets</h3>
          {datasets.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              className="rounded-lg h-8 text-xs"
              onClick={() => router.push('/hr/dataeditor')}
            >
              Import Data <Upload className="h-3 w-3 ml-1" />
            </Button>
          )}
        </div>

        {datasets.length === 0 ? (
          // Beautiful Empty State for Datasets
          <Card className="border border-dashed rounded-2xl">
            <CardContent className="flex flex-col items-center justify-center p-16 text-center">
              <div className="relative mb-8">
                <div className="p-6 bg-violet-500/10 rounded-3xl">
                  <FileSpreadsheet className="h-12 w-12 text-violet-600" />
                </div>
                <div className="absolute -top-2 -right-2 p-2 bg-primary/10 rounded-full">
                  <Upload className="h-4 w-4 text-primary" />
                </div>
              </div>
              <h3 className="text-2xl font-semibold mb-3">No Datasets Yet</h3>
              <p className="text-muted-foreground mb-8 max-w-md leading-relaxed">
                Import your first dataset to start your data analysis journey.
                Upload CSV, Excel files, or connect to your data sources.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={() => router.push('/hr/dataeditor')}
                  className="rounded-xl"
                  size="lg"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Import Your First Dataset
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowCreateModal(true)}
                  className="rounded-xl"
                  size="lg"
                >
                  <FolderPlus className="h-4 w-4 mr-2" />
                  Create Workspace First
                </Button>
              </div>
              <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-4 w-full max-w-md">
                <div className="text-center p-4 bg-muted/50 rounded-xl">
                  <Upload className="h-6 w-6 text-violet-600 mx-auto mb-2" />
                  <p className="text-sm font-medium">Import</p>
                  <p className="text-xs text-muted-foreground">CSV, Excel files</p>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-xl">
                  <Activity className="h-6 w-6 text-emerald-600 mx-auto mb-2" />
                  <p className="text-sm font-medium">Analyze</p>
                  <p className="text-xs text-muted-foreground">Explore your data</p>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-xl">
                  <TrendingUp className="h-6 w-6 text-primary mx-auto mb-2" />
                  <p className="text-sm font-medium">Insights</p>
                  <p className="text-xs text-muted-foreground">Generate reports</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {datasets.slice(0, 4).map((dataset) => (
              <Card key={dataset.id} className="rounded-xl hover:shadow-sm transition-all duration-200 group">
                <CardHeader className="pb-2 p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-violet-500/10 rounded-lg">
                        <FileSpreadsheet className="h-3 w-3 text-violet-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-xs font-semibold line-clamp-1">
                          {dataset.name}
                        </CardTitle>
                        <Badge variant="outline" className="text-xs mt-0.5 h-4">
                          {dataset.fileType.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  {dataset.description && (
                    <CardDescription className="line-clamp-1 text-xs mt-1">
                      {dataset.description}
                    </CardDescription>
                  )}
                </CardHeader>

                <CardContent className="space-y-2 p-3 pt-0">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-center p-1.5 bg-muted/50 rounded-lg">
                      <p className="text-xs font-semibold">{dataset.rowCount.toLocaleString()}</p>
                      <p className="text-xs text-muted-foreground">Rows</p>
                    </div>
                    <div className="text-center p-1.5 bg-muted/50 rounded-lg">
                      <p className="text-xs font-semibold">{dataset.headers.length}</p>
                      <p className="text-xs text-muted-foreground">Cols</p>
                    </div>
                  </div>

                  <div className="text-xs text-muted-foreground flex items-center gap-1">
                    <Calendar className="h-2 w-2" />
                    <span>{formatDate(dataset.createdAt)}</span>
                  </div>

                  <Button
                    onClick={() => router.push(`/hr/dataeditor?dataset=${dataset.id}`)}
                    className="w-full rounded-lg h-7 text-xs"
                    size="sm"
                  >
                    View <ArrowRight className="h-2 w-2 ml-1" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Server Monitoring Section - Real-time with Charts */}
      {serverStatus && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">Server Status</h3>
              <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-xs font-medium border ${getStatusColor(connectionStatus)}`}>
                {getStatusIcon(connectionStatus)}
                {connectionStatus === 'connected' ? 'Healthy' :
                 connectionStatus === 'warning' ? 'Load' : 'Down'}
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="rounded-lg h-8 text-xs"
              onClick={() => router.push('/hr/monitoring')}
            >
              Details <Activity className="h-3 w-3 ml-1" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {/* Curved CPU Card */}
            <Card
              className="rounded-xl"
              style={{
                borderRadius: '1rem 1rem 2rem 1rem'
              }}
            >
              <CardHeader className="pb-2 p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-emerald-500/10 rounded-lg">
                      <Cpu className="h-3 w-3 text-emerald-600" />
                    </div>
                    <CardTitle className="text-xs font-semibold">
                      CPU
                    </CardTitle>
                  </div>
                  <span className="text-lg font-bold">
                    {serverStatus.cpu_usage.toFixed(1)}%
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-2 p-3 pt-0">
                <Progress
                  value={serverStatus.cpu_usage}
                  className="h-1.5"
                />
                {metricsHistory.length > 0 && (
                  <div className="h-12">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={metricsHistory}>
                        <defs>
                          <linearGradient id="cpuGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                            <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <Tooltip
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div className="bg-background border rounded-lg p-2 shadow-md">
                                  <p className="text-xs font-medium">{`${label}`}</p>
                                  <p className="text-xs text-emerald-600">
                                    {/* @ts-ignore */}
                                    {`CPU: ${payload[0].value?.toFixed(1)}%`}
                                  </p>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="cpu"
                          stroke="#10b981"
                          strokeWidth={1.5}
                          fill="url(#cpuGradient)"
                          dot={false}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                )}
                <div className="text-xs text-muted-foreground flex items-center gap-1">
                  <TrendingUp className="h-2 w-2" />
                  <span>Live</span>
                </div>
              </CardContent>
            </Card>

            {/* Memory Card */}
            <Card className="rounded-xl">
              <CardHeader className="pb-2 p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-blue-500/10 rounded-lg">
                      <MemoryStick className="h-3 w-3 text-blue-600" />
                    </div>
                    <CardTitle className="text-xs font-semibold">
                      Memory
                    </CardTitle>
                  </div>
                  <span className="text-lg font-bold">
                    {serverStatus.memory.percent.toFixed(1)}%
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-2 p-3 pt-0">
                <Progress
                  value={serverStatus.memory.percent}
                  className="h-1.5"
                />
                {metricsHistory.length > 0 && (
                  <div className="h-12">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={metricsHistory}>
                        <defs>
                          <linearGradient id="memoryGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <Tooltip
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div className="bg-background border rounded-lg p-2 shadow-md">
                                  <p className="text-xs font-medium">{`${label}`}</p>
                                  <p className="text-xs text-blue-600">
                                    {/* @ts-ignore */}
                                    {`Memory: ${payload[0].value?.toFixed(1)}%`}
                                  </p>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="memory"
                          stroke="#3b82f6"
                          strokeWidth={1.5}
                          fill="url(#memoryGradient)"
                          dot={false}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                )}
                <div className="text-xs text-muted-foreground">
                  {formatBytes(serverStatus.memory.used)} / {formatBytes(serverStatus.memory.total)}
                </div>
              </CardContent>
            </Card>

            {/* Curved Disk Card */}
            <Card
              className="rounded-xl"
              style={{
                borderRadius: '1rem 2rem 1rem 1rem'
              }}
            >
              <CardHeader className="pb-2 p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-amber-500/10 rounded-lg">
                      <HardDrive className="h-3 w-3 text-amber-600" />
                    </div>
                    <CardTitle className="text-xs font-semibold">
                      Disk
                    </CardTitle>
                  </div>
                  <span className="text-lg font-bold">
                    {serverStatus.disk.percent.toFixed(1)}%
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-2 p-3 pt-0">
                <Progress
                  value={serverStatus.disk.percent}
                  className="h-1.5"
                />
                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>{formatBytes(serverStatus.disk.used)}</span>
                    <span>{formatBytes(serverStatus.disk.free)}</span>
                  </div>
                  <div className="text-xs text-muted-foreground text-center">
                    {formatBytes(serverStatus.disk.total)} total
                  </div>
                </div>
                <div className="text-xs text-muted-foreground flex items-center gap-1">
                  <Activity className="h-2 w-2" />
                  <span>Storage</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Create Workspace Modal */}
      <WorkspaceModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onWorkspaceCreated={handleWorkspaceCreated}
      />
    </div>
  )
}
