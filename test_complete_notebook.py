#!/usr/bin/env python3
"""
Complete notebook-style test with package installation
"""

# Test 1: Package installation (like Google Colab)
# !pip install requests

# Test 2: Simple interactive input
print("🎯 Complete Notebook Test")
print("=" * 30)

name = input("What's your name? ")
print(f"Hello, {name}!")

# Test 3: Some calculations
age = input("How old are you? ")
try:
    age_num = int(age)
    print(f"In 10 years, you'll be {age_num + 10} years old!")
except ValueError:
    print("That's not a valid age, but that's okay!")

# Test 4: Create some data
import matplotlib.pyplot as plt
import numpy as np

# Simple plot
x = np.linspace(0, 10, 100)
y = np.sin(x)

plt.figure(figsize=(8, 4))
plt.plot(x, y, 'b-', linewidth=2)
plt.title(f"Sine Wave for {name}")
plt.xlabel("X")
plt.ylabel("sin(X)")
plt.grid(True, alpha=0.3)
plt.show()

# Test 5: Final result
result = {"name": name, "age": age, "message": "Test completed!"}
result
