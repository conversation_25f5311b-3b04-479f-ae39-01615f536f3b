'use client'

import React, { useEffect, useRef, useState } from 'react'
import { cn } from "@/lib/utils"
import { Button } from '@/components/ui/button'
import { Pause, Play, Square } from 'lucide-react'

interface OutputRendererProps {
  output?: string
  plots?: string[]
  result?: any
  className?: string
  needsInput?: boolean
  inputPrompt?: string
  onInputSubmit?: (input: string) => void
}

export function OutputRenderer({
  output,
  plots,
  result,
  className,
  needsInput,
  inputPrompt,
  onInputSubmit
}: OutputRendererProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [userInput, setUserInput] = useState('')

  const handleSubmit = () => {
    if (userInput.trim() && onInputSubmit) {
      console.log('🚀 Submitting input immediately:', userInput.trim());
      onInputSubmit(userInput.trim())
      setUserInput('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSubmit()
    }
  }

  // Auto-focus input when needed
  useEffect(() => {
    if (needsInput && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [needsInput])

  // Enhanced detection for input requirements
  const hasInputPrompt = output && (
    output.includes('input(') ||
    output.includes('Enter') ||
    output.includes('What') ||
    output.includes('How old') ||
    output.includes('name?') ||
    output.includes('age?') ||
    (output.includes('?') && output.trim().endsWith('?')) ||
    output.includes('Please enter') ||
    output.includes('Type') ||
    output.trim().endsWith(':')
  )

  const showInput = needsInput || hasInputPrompt

  // Check if output contains camera/video related content
  const hasCameraContent = output && (
    output.includes('VideoCapture') ||
    output.includes('camera') ||
    output.includes('cv2') ||
    output.includes('mediapipe')
  )

  // Camera control functions
  const handleCameraControl = async (action: 'pause' | 'stop') => {
    try {
      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code: action === 'pause' ? 'pause_camera()' : 'stop_camera()',
          language: 'python',
          datasets: []
        })
      })
      if (response.ok) {
        console.log(`Camera ${action} executed`)
      }
    } catch (error) {
      console.error(`Error ${action}ing camera:`, error)
    }
  }

  return (
    <div className={cn("", className)}>
      {/* Camera Controls */}
      {hasCameraContent && (
        <div className="mb-3 flex gap-2 p-2 bg-blue-50 dark:bg-blue-950 rounded-md border border-blue-200 dark:border-blue-800">
          <span className="text-xs text-blue-700 dark:text-blue-300 font-medium flex-1">Camera Controls:</span>
          <Button
            size="sm"
            variant="outline"
            className="h-6 px-2 text-xs"
            onClick={() => handleCameraControl('pause')}
          >
            <Pause className="h-3 w-3 mr-1" />
            Pause
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
            onClick={() => handleCameraControl('stop')}
          >
            <Square className="h-3 w-3 mr-1" />
            Stop
          </Button>
        </div>
      )}

      {/* Combined Output Container - No borders, clean design */}
      {(output || plots?.length || result) && (
        <div className="font-mono text-sm bg-white dark:bg-gray-950 p-4 space-y-4">

          {/* Text Output */}
          {output && (
            <div>
              <pre className="whitespace-pre-wrap text-gray-900 dark:text-gray-100 leading-relaxed m-0 text-sm">
                {output}
              </pre>

              {/* Enhanced Jupyter-like input field */}
              {showInput && (
                <div className="mt-2 flex items-center gap-2 bg-blue-50 dark:bg-blue-900/30 px-3 py-2 rounded border border-blue-200 dark:border-blue-700">
                  <span className="text-blue-600 dark:text-blue-400 font-bold font-mono text-sm">In:</span>
                  <input
                    ref={inputRef}
                    type="text"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="flex-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-gray-900 dark:text-gray-100 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Type your input and press Enter..."
                    style={{ minWidth: '300px' }}
                    autoComplete="off"
                  />
                  <Button
                    size="sm"
                    onClick={handleSubmit}
                    disabled={!userInput.trim()}
                    className="h-7 px-3 text-xs bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Submit
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Plots - Clean display without cards */}
          {plots && plots.length > 0 && (
            <div className="space-y-4">
              {plots.map((plot, index) => {
                // Handle HTML plots (Plotly to_html() output)
                if (plot.includes('<div') && (plot.includes('plotly') || plot.includes('Plotly'))) {
                  return (
                    <div key={index} className="w-full">
                      <div
                        dangerouslySetInnerHTML={{ __html: plot }}
                        className="plotly-html-container w-full"
                        style={{ minHeight: '400px' }}
                      />
                    </div>
                  )
                }

                // Handle regular image plots (matplotlib, seaborn, etc.)
                let imgSrc = plot
                if (!plot.startsWith('data:') && !plot.startsWith('http')) {
                  imgSrc = `data:image/png;base64,${plot}`
                }
                return (
                  <div key={index} className="text-center">
                    <img
                      src={imgSrc}
                      alt={`Plot ${index + 1}`}
                      className="max-w-full h-auto mx-auto rounded shadow-sm"
                      style={{ maxHeight: '500px' }}
                      onError={(e) => {
                        console.error(`Failed to load plot ${index + 1}:`, imgSrc);
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                )
              })}
            </div>
          )}

          {/* Result - Clean display */}
          {result && (
            <div className="mt-4">
              <pre className="whitespace-pre-wrap text-gray-900 dark:text-gray-100 leading-relaxed m-0 text-sm bg-gray-50 dark:bg-gray-800 p-3 rounded">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}

        </div>
      )}
    </div>
  )
}
