import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

// Route Segment Config - Increase timeout for video processing
export const dynamic = 'force-dynamic';
export const maxDuration = 300; // 5 minutes for video processing

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { code, datasetIds, language, datasets, user_input } = body; // Extract datasets data and user_input
    const { getToken } = auth();

    // Debug logging for input submission
    if (user_input) {
      console.log('Input submission received:', {
        codeLength: code?.length || 0,
        language,
        user_input,
        hasCode: !!code
      });
    }

    // Validate required fields - allow empty code if user_input is provided (for input continuation)
    if ((!code && !user_input) || !language) {
      console.log('Validation failed:', { code: !!code, language, user_input: !!user_input });
      return NextResponse.json(
        { error: 'Missing required fields: code and language are required (unless providing user_input)' },
        { status: 400 }
      );
    }

    // Check if this is a SQL query sent to the wrong endpoint
    if (language === 'sql') {
      return NextResponse.json(
        { error: 'SQL queries should be sent to /api/query endpoint, not /api/execute' },
        { status: 400 }
      );
    }

    // Only support Python execution in this endpoint
    if (language !== 'python') {
      return NextResponse.json(
        { error: `Unsupported language: ${language}. This endpoint only supports Python execution.` },
        { status: 400 }
      );
    }

    // For Python, we can work without datasets (user can load their own data)
    const datasetId = datasetIds && datasetIds.length > 0 ? datasetIds[0] : body.datasetId || 'no-dataset';

    // Skip authentication for now to get it working
    const token = "no-auth-needed";

    // Call the Python backend with retry logic
    const pythonBackendUrl = process.env.PYTHON_BACKEND_URL || 'http://127.0.0.1:8000';

    // Add retry logic for the Python backend - enhanced for video processing
    const MAX_RETRIES = 3; // Increased retries for video processing
    let retries = 0;
    let response: Response | undefined;

    // Check if this is likely video processing code
    const isVideoProcessing = code && (
      code.includes('cv2.VideoCapture') ||
      code.includes('VideoFileClip') ||
      code.includes('.mp4') ||
      code.includes('.avi') ||
      code.includes('video') ||
      code.includes('frame')
    );

    const timeout = isVideoProcessing ? 600000 : 300000; // 10 minutes for video, 5 for others
    console.log(`🎥 Video processing detected: ${isVideoProcessing}, timeout: ${timeout/1000}s`);

    while (retries <= MAX_RETRIES) {
      try {
        // Call the Python backend
        response = await fetch(`${pythonBackendUrl}/api/execute`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code: code,  // Changed from 'query' to 'code' to match backend
            language: language,
            datasetId: datasetId,
            datasetIds: datasetIds, // Pass the full array of dataset IDs
            datasets: datasets, // Pass the actual dataset data
            user_input: user_input, // Pass the user input for interactive execution
          }),
          // Dynamic timeout based on content type
          signal: AbortSignal.timeout(timeout),
        });

        // If successful, break out of the retry loop
        break;
      } catch (fetchError) {
        retries++;
        console.error(`🔄 Fetch attempt ${retries} failed:`, fetchError);

        // Special handling for timeout errors
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          console.error(`⏰ Request timed out after ${timeout/1000} seconds`);
          if (isVideoProcessing) {
            throw new Error(`Video processing timed out after ${timeout/1000} seconds. Try processing smaller video files or shorter clips.`);
          }
        }

        // If we've reached max retries, throw the error
        if (retries > MAX_RETRIES) {
          const errorMsg = isVideoProcessing
            ? `Failed to process video after ${MAX_RETRIES} attempts. The video file might be too large or the server is overloaded.`
            : `Failed to connect to Python server after ${MAX_RETRIES} attempts. The server might be down.`;
          throw new Error(errorMsg);
        }

        // Wait before retrying (exponential backoff, longer for video)
        const waitTime = isVideoProcessing ? 3000 * retries : 1000 * retries;
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    // Ensure response is defined before using it
    if (!response) {
      throw new Error('Failed to connect to Python server. No response received.');
    }

    if (!response.ok) {
      // Safely try to parse the error response as JSON
      let errorMessage = `HTTP error ${response.status}`;
      try {
        const errorText = await response.text();
        // Try to parse as JSON
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
          // If it's not valid JSON, use the text directly (truncated if too long)
          errorMessage = errorText.length > 200
            ? `${errorText.substring(0, 200)}...`
            : errorText;
        }
      } catch (textError) {
        // If we can't even get the text, use a generic error
        console.error('Failed to read error response:', textError);
      }

      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    // Safely parse the response as JSON
    let result: any;
    try {
      const responseText = await response.text();
      try {
        result = JSON.parse(responseText);
      } catch (jsonError) {
        console.error('Failed to parse response as JSON:', jsonError);
        console.log('Response text:', responseText.substring(0, 200) + '...');
        throw new Error('Invalid JSON response from server');
      }
    } catch (textError) {
      console.error('Failed to read response:', textError);
      throw new Error('Failed to read response from server');
    }
    return NextResponse.json(result);
  } catch (error) {
    console.error('Code execution error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to execute code' },
      { status: 500 }
    );
  }
}




