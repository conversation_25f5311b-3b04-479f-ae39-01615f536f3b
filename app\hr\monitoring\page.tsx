'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { RefreshCw, Server, Package, Activity, HardDrive, Cpu, MemoryStick, TrendingUp, CheckCircle, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, Area, AreaChart, Tooltip } from 'recharts'

interface ServerStatus {
  status: string
  python_version: string
  server_uptime: number
  cpu_usage: number
  memory: {
    total: number
    available: number
    percent: number
    used: number
  }
  disk: {
    total: number
    free: number
    used: number
    percent: number
  }
  process: {
    pid: number
    memory_info: any
    cpu_percent: number
    create_time: number
  }
  kernel: {
    execution_count: number
    namespace_variables: number
  }
  packages: {
    total_count: number
    packages: Array<{
      name: string
      version: string
      location: string
    }>
  }
}

interface MetricDataPoint {
  time: string
  cpu: number
  memory: number
  timestamp: number
}

export default function MonitoringPage() {
  const [status, setStatus] = useState<ServerStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [metricsHistory, setMetricsHistory] = useState<MetricDataPoint[]>([])
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'warning' | 'disconnected'>('disconnected')

  const updateMetricsHistory = (newStatus: ServerStatus) => {
    const now = new Date()
    const timeString = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })

    const newDataPoint: MetricDataPoint = {
      time: timeString,
      cpu: newStatus.cpu_usage,
      memory: newStatus.memory.percent,
      timestamp: now.getTime()
    }

    setMetricsHistory(prev => {
      const updated = [...prev, newDataPoint]
      // Keep only last 30 data points for detailed monitoring
      return updated.slice(-30)
    })

    // Determine connection status based on metrics
    if (newStatus.status === 'running') {
      if (newStatus.cpu_usage > 80 || newStatus.memory.percent > 85) {
        setConnectionStatus('warning')
      } else {
        setConnectionStatus('connected')
      }
    } else {
      setConnectionStatus('disconnected')
    }
  }

  const fetchStatus = async () => {
    try {
      setLoading(true)
      // Call the Next.js API route that proxies to the Python backend
      const response = await fetch('/api/monitoring')

      if (response.ok) {
        const data = await response.json()
        setStatus(data)
        updateMetricsHistory(data)
        setError(null)
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      setConnectionStatus('disconnected')
      toast.error('Failed to fetch server status')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStatus()
  }, [])

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchStatus, 5000) // Refresh every 5 seconds
      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours}h ${minutes}m ${secs}s`
  }

  const getPackageDisplayName = (pkg: { name: string; location: string }) => {
    // In cloud environments, just show package name without path
    if (pkg.location.includes('/opt/') || pkg.location.includes('/usr/') || pkg.location.includes('site-packages')) {
      return pkg.name
    }
    return pkg.name
  }

  const getStatusColor = (status: 'connected' | 'warning' | 'disconnected') => {
    switch (status) {
      case 'connected':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200'
      case 'warning':
        return 'text-amber-600 bg-amber-50 border-amber-200'
      case 'disconnected':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusIcon = (status: 'connected' | 'warning' | 'disconnected') => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4" />
      case 'warning':
        return <AlertCircle className="h-4 w-4" />
      case 'disconnected':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Server className="h-4 w-4" />
    }
  }

  if (loading && !status) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading server status...</span>
        </div>
      </div>
    )
  }

  if (error && !status) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={fetchStatus} className="mt-4">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">Server Monitoring</h1>
            <div className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium border ${getStatusColor(connectionStatus)}`}>
              {getStatusIcon(connectionStatus)}
              {connectionStatus === 'connected' ? 'Healthy' :
               connectionStatus === 'warning' ? 'High Load' : 'Disconnected'}
            </div>
          </div>
          <p className="text-muted-foreground">Real-time server status and performance monitoring</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={autoRefresh ? "default" : "outline"}
            size="sm"
            className="rounded-xl"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Activity className="h-4 w-4 mr-2" />
            Auto Refresh
          </Button>
          <Button
            onClick={fetchStatus}
            size="sm"
            disabled={loading}
            className="rounded-xl"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {status && (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="packages">Packages ({status.packages.total_count})</TabsTrigger>
            <TabsTrigger value="kernel">Kernel</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Real-time Performance Charts */}
            {metricsHistory.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="rounded-2xl">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-emerald-600" />
                      CPU Usage Over Time
                    </CardTitle>
                    <CardDescription>Real-time CPU performance monitoring</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={metricsHistory}>
                          <defs>
                            <linearGradient id="cpuGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                              <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                            </linearGradient>
                          </defs>
                          <XAxis
                            dataKey="time"
                            tick={{ fontSize: 12 }}
                            tickLine={false}
                            axisLine={false}
                          />
                          <YAxis
                            domain={[0, 100]}
                            tick={{ fontSize: 12 }}
                            tickLine={false}
                            axisLine={false}
                          />
                          <Tooltip
                            content={({ active, payload, label }) => {
                              if (active && payload && payload.length) {
                                return (
                                  <div className="bg-background border rounded-lg p-3 shadow-md">
                                    <p className="text-sm font-medium">{`Time: ${label}`}</p>
                                    <p className="text-sm text-emerald-600">
                                      {/* @ts-ignore */}
                                      {`CPU Usage: ${payload[0].value?.toFixed(1)}%`}
                                    </p>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <Area
                            type="monotone"
                            dataKey="cpu"
                            stroke="#10b981"
                            strokeWidth={2}
                            fill="url(#cpuGradient)"
                            dot={false}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card className="rounded-2xl">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                      Memory Usage Over Time
                    </CardTitle>
                    <CardDescription>Real-time memory performance monitoring</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={metricsHistory}>
                          <defs>
                            <linearGradient id="memoryGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                              <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                            </linearGradient>
                          </defs>
                          <XAxis
                            dataKey="time"
                            tick={{ fontSize: 12 }}
                            tickLine={false}
                            axisLine={false}
                          />
                          <YAxis
                            domain={[0, 100]}
                            tick={{ fontSize: 12 }}
                            tickLine={false}
                            axisLine={false}
                          />
                          <Tooltip
                            content={({ active, payload, label }) => {
                              if (active && payload && payload.length) {
                                return (
                                  <div className="bg-background border rounded-lg p-3 shadow-md">
                                    <p className="text-sm font-medium">{`Time: ${label}`}</p>
                                    <p className="text-sm text-blue-600">
                                      {/* @ts-ignore */}
                                      {`Memory Usage: ${payload[0].value?.toFixed(1)}%`}
                                    </p>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <Area
                            type="monotone"
                            dataKey="memory"
                            stroke="#3b82f6"
                            strokeWidth={2}
                            fill="url(#memoryGradient)"
                            dot={false}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="rounded-2xl">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Server Status</CardTitle>
                  <Server className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    <Badge
                      variant={status.status === 'running' ? 'default' : 'destructive'}
                      className="rounded-xl"
                    >
                      {status.status}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Uptime: {formatUptime(status.server_uptime)}
                  </p>
                </CardContent>
              </Card>

              <Card className="rounded-2xl">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
                  <Cpu className="h-4 w-4 text-emerald-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{status.cpu_usage.toFixed(1)}%</div>
                  <Progress
                    value={status.cpu_usage}
                    className="mt-2 h-2"
                  />
                </CardContent>
              </Card>

              <Card className="rounded-2xl">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
                  <MemoryStick className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{status.memory.percent.toFixed(1)}%</div>
                  <Progress
                    value={status.memory.percent}
                    className="mt-2 h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {formatBytes(status.memory.used)} / {formatBytes(status.memory.total)}
                  </p>
                </CardContent>
              </Card>

              <Card className="rounded-2xl">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
                  <HardDrive className="h-4 w-4 text-amber-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{status.disk.percent.toFixed(1)}%</div>
                  <Progress
                    value={status.disk.percent}
                    className="mt-2 h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {formatBytes(status.disk.used)} / {formatBytes(status.disk.total)}
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Python Environment</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <span className="font-medium">Python Version:</span>
                    <p className="text-sm text-muted-foreground">{status.python_version}</p>
                  </div>
                  <div>
                    <span className="font-medium">Process ID:</span>
                    <p className="text-sm text-muted-foreground">{status.process.pid}</p>
                  </div>
                  <div>
                    <span className="font-medium">Process CPU:</span>
                    <p className="text-sm text-muted-foreground">{status.process.cpu_percent.toFixed(1)}%</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Kernel Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <span className="font-medium">Execution Count:</span>
                    <p className="text-sm text-muted-foreground">{status.kernel.execution_count}</p>
                  </div>
                  <div>
                    <span className="font-medium">Namespace Variables:</span>
                    <p className="text-sm text-muted-foreground">{status.kernel.namespace_variables}</p>
                  </div>
                  <div>
                    <span className="font-medium">Total Packages:</span>
                    <p className="text-sm text-muted-foreground">{status.packages.total_count}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="packages" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Installed Packages ({status.packages.total_count})
                </CardTitle>
                <CardDescription>
                  All Python packages currently installed in the server environment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="max-h-96 overflow-y-auto">
                  <div className="grid gap-2">
                    {status.packages.packages.map((pkg, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <span className="font-medium">{getPackageDisplayName(pkg)}</span>
                          <Badge variant="outline" className="ml-2">{pkg.version}</Badge>
                        </div>
                        <span className="text-xs text-muted-foreground truncate max-w-xs">
                          {pkg.location.includes('/opt/') || pkg.location.includes('/usr/') ? 
                            'System Package' : 
                            pkg.location.split('/').pop() || 'Local Package'
                          }
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="kernel" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Jupyter Kernel Status</CardTitle>
                <CardDescription>
                  Information about the current Python execution kernel
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">Execution Count:</span>
                    <p className="text-2xl font-bold">{status.kernel.execution_count}</p>
                  </div>
                  <div>
                    <span className="font-medium">Active Variables:</span>
                    <p className="text-2xl font-bold">{status.kernel.namespace_variables}</p>
                  </div>
                </div>
                <div className="pt-4">
                  <Button 
                    variant="destructive" 
                    onClick={() => {
                      // TODO: Add kernel reset functionality
                      toast.info('Kernel reset functionality coming soon')
                    }}
                  >
                    Reset Kernel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}
