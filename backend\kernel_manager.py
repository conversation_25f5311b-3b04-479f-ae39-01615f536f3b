"""
Jupyter Kernel Management Module

This module handles the lifecycle and management of Jupyter kernel instances.
It provides functionality for:
- Creating and managing kernel instances
- Handling kernel state and persistence
- Managing variable contexts across executions
- Cleanup and resource management
"""

import logging
from typing import Dict, Any, Optional
from jupyter_kernel import get_kernel as _get_kernel, reset_kernel as _reset_kernel

logger = logging.getLogger(__name__)


def get_kernel():
    """
    Get or create the global Jupyter kernel instance
    Uses the existing kernel management from jupyter_kernel module

    Returns:
        JupyterKernel: The active kernel instance
    """
    return _get_kernel()


def reset_kernel():
    """
    Reset the kernel by creating a new instance
    Uses the existing reset functionality from jupyter_kernel module

    Returns:
        JupyterKernel: New kernel instance
    """
    return _reset_kernel()


def execute_code_with_kernel(
    code: str, 
    datasets: list = None, 
    variable_context: Dict[str, Any] = None,
    user_input: str = None
) -> Dict[str, Any]:
    """
    Execute code using the managed kernel instance
    
    Args:
        code: Python code to execute
        datasets: List of datasets to make available
        variable_context: Variables to inject into execution context
        user_input: User input for interactive execution
        
    Returns:
        Dictionary containing execution results
    """
    kernel = get_kernel()
    
    try:
        # Handle user input if provided
        if user_input is not None:
            # Store user input for the next input() call
            if not hasattr(kernel, '_user_input_queue'):
                kernel._user_input_queue = []
            kernel._user_input_queue.append(user_input)
            logger.debug(f"Received user input: {user_input}")

        # Prepare datasets if provided
        if datasets:
            _prepare_datasets_for_kernel(kernel, datasets)
        
        # Inject variable context if provided
        if variable_context:
            _inject_variable_context(kernel, variable_context)
        
        # Execute the code
        result = kernel.execute_code(code)
        
        return result
        
    except Exception as e:
        logger.error(f"Error executing code with kernel: {str(e)}")
        return {
            'status': 'error',
            'error': str(e),
            'stdout': '',
            'stderr': str(e),
            'plots': [],
            'result': None
        }


def _prepare_datasets_for_kernel(kernel: JupyterKernel, datasets: list) -> None:
    """Prepare and inject datasets into kernel namespace"""
    import pandas as pd
    import os
    
    try:
        # Create CSV files in temp directory for each dataset
        for i, dataset in enumerate(datasets):
            if isinstance(dataset, dict) and 'data' in dataset:
                df = pd.DataFrame(dataset['data'])
                dataset_name = dataset.get('name', f'dataset_{i}')
                
                # Clean dataset name for filename
                clean_name = ''.join(c for c in dataset_name if c.isalnum() or c in ('_', '-')).lower()
                csv_path = os.path.join(kernel.temp_dir, f'{clean_name}.csv')
                
                # Save as CSV
                df.to_csv(csv_path, index=False)
                
                # Also inject as variable
                var_name = f'df{i+1}' if i > 0 else 'df'
                kernel.namespace[var_name] = df
                
                logger.debug(f"Dataset '{dataset_name}' prepared as {var_name} and {clean_name}.csv")
                
    except Exception as e:
        logger.warning(f"Error preparing datasets: {e}")


def _inject_variable_context(kernel: JupyterKernel, variable_context: Dict[str, Any]) -> None:
    """Inject variable context into kernel namespace"""
    try:
        for var_name, var_value in variable_context.items():
            kernel.namespace[var_name] = var_value
            logger.debug(f"Injected variable: {var_name}")
            
    except Exception as e:
        logger.warning(f"Error injecting variable context: {e}")


def get_kernel_variables() -> Dict[str, Any]:
    """
    Get current variables from kernel namespace
    
    Returns:
        Dictionary of variable names and their types/info
    """
    kernel = get_kernel()
    
    try:
        variables = {}
        for name, value in kernel.namespace.items():
            if not name.startswith('_') and not callable(value):
                try:
                    var_type = type(value).__name__
                    var_info = {
                        'type': var_type,
                        'value': str(value)[:100] if len(str(value)) <= 100 else str(value)[:100] + '...'
                    }
                    
                    # Add special info for DataFrames
                    if hasattr(value, 'shape'):
                        var_info['shape'] = value.shape
                    
                    variables[name] = var_info
                    
                except Exception:
                    # Skip variables that can't be serialized
                    continue
                    
        return variables
        
    except Exception as e:
        logger.error(f"Error getting kernel variables: {e}")
        return {}


def cleanup_kernel() -> None:
    """Cleanup the kernel instance and resources"""
    global _kernel_instance
    
    if _kernel_instance is not None:
        try:
            _kernel_instance.cleanup()
            logger.info("Kernel cleaned up successfully")
        except Exception as e:
            logger.error(f"Error cleaning up kernel: {e}")
        finally:
            _kernel_instance = None


def get_kernel_status() -> Dict[str, Any]:
    """
    Get current kernel status and information
    
    Returns:
        Dictionary containing kernel status information
    """
    global _kernel_instance
    
    if _kernel_instance is None:
        return {
            'status': 'not_initialized',
            'execution_count': 0,
            'variables_count': 0
        }
    
    try:
        variables = get_kernel_variables()
        
        return {
            'status': 'active',
            'execution_count': getattr(_kernel_instance, 'execution_count', 0),
            'variables_count': len(variables),
            'has_temp_dir': hasattr(_kernel_instance, 'temp_dir'),
            'temp_dir': getattr(_kernel_instance, 'temp_dir', None)
        }
        
    except Exception as e:
        logger.error(f"Error getting kernel status: {e}")
        return {
            'status': 'error',
            'error': str(e)
        }
