#!/usr/bin/env python3
"""
Test that the sys import fix works and monitoring is accessible
"""

print("🧪 Testing Sys Import Fix")
print("=" * 40)

# Test 1: Basic sys access (should work now)
import sys
print(f"✅ Python version: {sys.version}")
print(f"✅ Python executable: {sys.executable}")

# Test 2: Basic imports (should work without errors)
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

print("✅ Core libraries imported successfully")

# Test 3: Create a simple plot (should work silently)
x = np.linspace(0, 10, 50)
y = np.sin(x)

plt.figure(figsize=(8, 4))
plt.plot(x, y, 'b-', linewidth=2)
plt.title('Test Plot - Sys Import Fixed')
plt.xlabel('X')
plt.ylabel('sin(X)')
plt.grid(True, alpha=0.3)
plt.show()

# Test 4: DataFrame creation
df = pd.DataFrame({
    'x': x[:10],
    'y': y[:10],
    'category': ['A'] * 5 + ['B'] * 5
})

print("✅ DataFrame created successfully")
print(f"DataFrame shape: {df.shape}")

# Test 5: Try Plotly (should work silently now)
try:
    import plotly.graph_objects as go
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=x, y=y, mode='lines', name='sine'))
    fig.update_layout(title='Test Plotly - No Verbose Output')
    fig.show()
    
    print("✅ Plotly imported and used successfully (silently)")
except ImportError:
    print("ℹ️ Plotly not available (this is fine)")

print("\n🎉 All tests completed!")
print("✅ Sys import error should be fixed")
print("✅ Server should start cleanly")
print("✅ Monitoring page should work")
print("✅ No verbose Plotly output")

# Return the DataFrame for table display
df
