import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Call the Python backend monitoring endpoint
    const pythonBackendUrl = process.env.PYTHON_BACKEND_URL || 'https://flopbackend.onrender.com';
    
    const response = await fetch(`${pythonBackendUrl}/api/server/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add a timeout to prevent hanging
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Backend server error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Monitoring API error:', error);
    
    if (error instanceof Error && error.name === 'AbortError') {
      return NextResponse.json(
        { error: 'Request timeout - Python backend may be down' },
        { status: 504 }
      );
    }
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch server status' },
      { status: 500 }
    );
  }
}
