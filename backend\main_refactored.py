"""
H<PERSON><PERSON>las Backend API Server (Refactored)

This is the main FastAPI server that provides:
- Python code execution via Jupyter kernel
- SQL query execution  
- Dataset management
- Streamlit app creation and management
- Server monitoring and health checks

The server uses modular components for better maintainability:
- kernel_manager: Handles Jupyter kernel lifecycle
- streamlit_manager: Manages Streamlit app creation and deployment
- response_utils: Standardizes API responses and data formatting
- server_monitoring: Provides system monitoring and health checks
- request_models: Defines all API request/response models
"""

import logging
import traceback
import os
import time
from typing import Dict, Any, List, Optional

from fastapi import FastAPI, HTTPException, Header, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse, HTMLResponse
from dotenv import load_dotenv

# Import our modular components
from kernel_manager import get_kernel, execute_code_with_kernel, get_kernel_status
from streamlit_manager import create_streamlit_app, cleanup_streamlit_app, serve_streamlit_app_page
from response_utils import (
    format_execution_response, 
    create_error_response, 
    create_success_response,
    format_streamlit_response,
    CustomJSONEncoder
)
from server_monitoring import get_server_status, get_installed_packages
from request_models import QueryRequest, SQLQueryRequest, JSQueryRequest

# Load environment variables
load_dotenv()
NEXTJS_API_URL = os.getenv('NEXTJS_API_URL', 'http://localhost:3000')
CLERK_SECRET_KEY = os.getenv('CLERK_SECRET_KEY')

# Track server start time
start_time = time.time()

# Setup logging - reduced verbosity
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(title="HRatlas Backend", version="2.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint with server information"""
    return {
        "message": "HRatlas Backend API",
        "version": "2.0.0",
        "status": "running",
        "uptime": time.time() - start_time
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        kernel_status = get_kernel_status()
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "kernel": kernel_status,
            "uptime": time.time() - start_time
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }


@app.post("/api/execute-jupyter")
async def execute_jupyter(request: Request, query_request: QueryRequest):
    """
    Jupyter-like code execution endpoint using the JupyterKernel
    """
    try:
        code = query_request.code
        datasets = query_request.datasets or []
        user_input = getattr(query_request, 'user_input', None)

        # Execute code using kernel manager
        result = execute_code_with_kernel(
            code=code,
            datasets=datasets,
            variable_context=query_request.variableContext,
            user_input=user_input
        )

        # Format response using response utilities
        response_data = format_execution_response(result)
        
        return response_data

    except Exception as e:
        logger.error(f"Error in execute_jupyter: {str(e)}")
        logger.error(traceback.format_exc())
        return create_error_response(
            error_message=str(e),
            error_details={
                'message': str(e),
                'code': 'ExecutionError',
                'stack': traceback.format_exc()
            }
        )


@app.post("/api/execute")
async def execute_legacy(request: Request, query_request: QueryRequest):
    """
    Legacy endpoint that redirects to the new Jupyter kernel
    Maintains compatibility with existing frontend code
    """
    # Just call the new Jupyter endpoint
    return await execute_jupyter(request, query_request)


@app.post("/api/python/continue")
async def continue_with_input(request: Request):
    """Continue Python execution with user input"""
    try:
        data = await request.json()
        user_input = data.get('input', '')
        original_code = data.get('code', '')

        # Get the kernel instance
        kernel = get_kernel()

        # Continue execution with the provided input
        result = kernel.provide_input_and_continue(user_input, original_code)

        # Format response using response utilities
        response_data = format_execution_response(result)
        
        return response_data

    except Exception as e:
        logger.error(f"Error continuing execution: {str(e)}")
        return create_error_response(f"Execution failed: {str(e)}")


@app.get("/streamlit/{app_id}")
async def serve_streamlit_app(app_id: str):
    """Serve Streamlit app code for cloud deployment"""
    try:
        html_content = serve_streamlit_app_page(app_id)
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        logger.error(f"Error serving Streamlit app: {str(e)}")
        return HTMLResponse(content=f"<h1>Error</h1><p>{str(e)}</p>", status_code=500)


@app.post("/api/streamlit/stop/{app_id}")
async def stop_streamlit_app(app_id: str):
    """Stop a running Streamlit app"""
    try:
        result = cleanup_streamlit_app(app_id)
        return result
    except Exception as e:
        logger.error(f"Error stopping Streamlit app: {str(e)}")
        return create_error_response(f"Failed to stop app: {str(e)}")


@app.get("/api/server/status")
async def server_status():
    """Get comprehensive server status"""
    try:
        status = get_server_status()
        return status
    except Exception as e:
        logger.error(f"Error getting server status: {str(e)}")
        return create_error_response(f"Failed to get server status: {str(e)}")


@app.get("/api/server/packages")
async def server_packages():
    """Get installed Python packages"""
    try:
        packages = get_installed_packages()
        return {"packages": packages}
    except Exception as e:
        logger.error(f"Error getting packages: {str(e)}")
        return create_error_response(f"Failed to get packages: {str(e)}")


@app.post("/api/query")
async def execute_sql_query(request: Request, query_request: SQLQueryRequest):
    """Execute SQL query (placeholder for future implementation)"""
    try:
        # For now, return a simple response
        # TODO: Implement SQL execution
        return create_success_response(
            data=[],
            output="SQL execution not implemented in simplified version"
        )

    except Exception as e:
        logger.error(f"Error in SQL query execution: {str(e)}")
        return create_error_response(str(e))


@app.post("/api/execute-js")
async def execute_javascript(request: Request, query_request: JSQueryRequest):
    """Execute JavaScript code (placeholder for future implementation)"""
    try:
        # For now, return a simple response
        # TODO: Implement JavaScript execution
        return create_success_response(
            data=[],
            output="JavaScript execution not implemented in simplified version"
        )

    except Exception as e:
        logger.error(f"Error in JavaScript execution: {str(e)}")
        return create_error_response(str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
