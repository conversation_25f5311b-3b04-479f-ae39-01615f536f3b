'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { Upload, FileSpreadsheet, Database, BarChart3, TrendingUp, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface EmptyStateIllustrationProps {
  onUploadClick: () => void;
}

export const EmptyStateIllustration: React.FC<EmptyStateIllustrationProps> = ({ onUploadClick }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
      {/* Animated Illustration */}
      <div className="relative mb-8">
        {/* Main Upload Circle */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="relative"
        >
          <div className="w-32 h-32 rounded-full bg-gradient-to-br from-primary/20 via-primary/10 to-transparent border-2 border-dashed border-primary/30 flex items-center justify-center">
            <motion.div
              animate={{ 
                y: [0, -8, 0],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="p-4 bg-primary/10 rounded-2xl"
            >
              <Upload className="h-8 w-8 text-primary" />
            </motion.div>
          </div>
        </motion.div>

        {/* Floating Data Icons */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="absolute -left-8 top-4"
        >
          <motion.div
            animate={{ 
              y: [0, -6, 0],
              rotate: [0, 10, 0]
            }}
            transition={{ 
              duration: 2.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.5
            }}
            className="p-2 bg-emerald-500/10 rounded-lg border border-emerald-500/20"
          >
            <FileSpreadsheet className="h-5 w-5 text-emerald-600" />
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="absolute -right-8 top-8"
        >
          <motion.div
            animate={{ 
              y: [0, -4, 0],
              rotate: [0, -8, 0]
            }}
            transition={{ 
              duration: 2.8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
            className="p-2 bg-violet-500/10 rounded-lg border border-violet-500/20"
          >
            <Database className="h-5 w-5 text-violet-600" />
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="absolute -bottom-4 left-4"
        >
          <motion.div
            animate={{ 
              y: [0, -5, 0],
              rotate: [0, 12, 0]
            }}
            transition={{ 
              duration: 3.2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1.5
            }}
            className="p-2 bg-blue-500/10 rounded-lg border border-blue-500/20"
          >
            <BarChart3 className="h-5 w-5 text-blue-600" />
          </motion.div>
        </motion.div>

        {/* Connecting Lines */}
        <motion.div
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 0.3 }}
          transition={{ delay: 0.8, duration: 1.5, ease: "easeInOut" }}
          className="absolute inset-0 pointer-events-none"
        >
          <svg className="w-full h-full" viewBox="0 0 128 128">
            <motion.path
              d="M 20 40 Q 64 20 108 60"
              stroke="currentColor"
              strokeWidth="1"
              fill="none"
              strokeDasharray="4 4"
              className="text-primary/20"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ delay: 1, duration: 2, repeat: Infinity }}
            />
            <motion.path
              d="M 20 100 Q 64 80 108 40"
              stroke="currentColor"
              strokeWidth="1"
              fill="none"
              strokeDasharray="4 4"
              className="text-primary/20"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ delay: 1.5, duration: 2, repeat: Infinity }}
            />
          </svg>
        </motion.div>
      </div>

      {/* Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.5 }}
        className="text-center space-y-4 max-w-md"
      >
        <h2 className="text-2xl font-bold">Welcome to Data Analytics</h2>
        <p className="text-muted-foreground leading-relaxed">
          Transform your data into insights. Upload CSV or Excel files to start analyzing, 
          visualizing, and discovering patterns in your data.
        </p>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.5 }}
        className="flex flex-col sm:flex-row gap-3 mt-8"
      >
        <Button 
          onClick={onUploadClick}
          size="lg"
          className="rounded-xl"
        >
          <Upload className="h-4 w-4 mr-2" />
          Upload Your Data
        </Button>
        <Button 
          variant="outline"
          size="lg"
          className="rounded-xl"
        >
          <TrendingUp className="h-4 w-4 mr-2" />
          View Examples
        </Button>
      </motion.div>

      {/* Feature Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1, duration: 0.5 }}
        className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-12 w-full max-w-2xl"
      >
        <Card className="border-0 bg-gradient-to-br from-emerald-50 to-emerald-100/50 dark:from-emerald-950/50 dark:to-emerald-900/30">
          <CardContent className="p-4 text-center">
            <div className="p-2 bg-emerald-500/10 rounded-lg w-fit mx-auto mb-2">
              <FileSpreadsheet className="h-5 w-5 text-emerald-600" />
            </div>
            <h3 className="font-semibold text-sm">Import Data</h3>
            <p className="text-xs text-muted-foreground mt-1">CSV, Excel files</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-950/50 dark:to-blue-900/30">
          <CardContent className="p-4 text-center">
            <div className="p-2 bg-blue-500/10 rounded-lg w-fit mx-auto mb-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-semibold text-sm">Visualize</h3>
            <p className="text-xs text-muted-foreground mt-1">Charts & graphs</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-violet-50 to-violet-100/50 dark:from-violet-950/50 dark:to-violet-900/30">
          <CardContent className="p-4 text-center">
            <div className="p-2 bg-violet-500/10 rounded-lg w-fit mx-auto mb-2">
              <Zap className="h-5 w-5 text-violet-600" />
            </div>
            <h3 className="font-semibold text-sm">Analyze</h3>
            <p className="text-xs text-muted-foreground mt-1">Get insights</p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};
