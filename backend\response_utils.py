"""
API Response Utilities Module

This module provides utilities for formatting and processing API responses.
It handles:
- Standardizing response formats across different endpoints
- Processing execution results into consistent structures
- Handling error responses and logging
- Data type conversions and serialization
"""

import json
import math
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for handling special data types"""
    
    def default(self, obj):
        if isinstance(obj, float):
            if math.isnan(obj):
                return "NaN"
            elif math.isinf(obj):
                return "Infinity" if obj > 0 else "-Infinity"
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            if np.isnan(obj):
                return "NaN"
            elif np.isinf(obj):
                return "Infinity" if obj > 0 else "-Infinity"
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, pd.Series):
            return obj.tolist()
        return super().default(obj)


def format_execution_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format execution result into standardized API response
    
    Args:
        result: Raw execution result from kernel
        
    Returns:
        Formatted response dictionary
    """
    try:
        response_data = {
            'status': result.get('status', 'ok'),
            'output': result.get('stdout', ''),
            'plots': result.get('plots', []),
            'execution_count': result.get('execution_count', 0)
        }

        # Add stderr to output if present
        if result.get('stderr'):
            if response_data['output']:
                response_data['output'] += f"\n{result['stderr']}"
            else:
                response_data['output'] = result['stderr']

        # Process result data
        if 'result' in result and result['result'] is not None:
            processed_result = process_result_data(result['result'])
            response_data.update(processed_result)

        # Handle input requests
        if result.get('needs_input'):
            response_data['needs_input'] = True
            response_data['input_prompt'] = result.get('input_prompt', 'Enter input:')

        # Add variable information
        if 'variables' in result:
            response_data['variables'] = result['variables']
        if 'variableTypes' in result:
            response_data['variableTypes'] = result['variableTypes']

        return response_data

    except Exception as e:
        logger.error(f"Error formatting execution response: {str(e)}")
        return create_error_response(f"Response formatting failed: {str(e)}")


def process_result_data(result_data: Any) -> Dict[str, Any]:
    """
    Process and format result data based on its type
    
    Args:
        result_data: The result data to process
        
    Returns:
        Dictionary with processed data and metadata
    """
    try:
        response = {'result': result_data}

        # Handle DataFrame results
        if isinstance(result_data, pd.DataFrame):
            response.update({
                'data': result_data.to_dict('records'),
                'columns': [{'name': col, 'type': str(result_data[col].dtype)} for col in result_data.columns],
                'outputType': 'dataframe',
                'result': {
                    'type': 'table',
                    'data': result_data.to_dict('records'),
                    'columns': list(result_data.columns)
                }
            })

        # Handle list/array results
        elif isinstance(result_data, (list, np.ndarray)):
            if isinstance(result_data, np.ndarray):
                result_data = result_data.tolist()
            
            response.update({
                'data': result_data,
                'outputType': 'array'
            })

        # Handle dictionary results
        elif isinstance(result_data, dict):
            response.update({
                'data': result_data,
                'outputType': 'object'
            })

        # Handle scalar results
        else:
            response.update({
                'data': [{'value': result_data}],
                'outputType': 'scalar'
            })

        return response

    except Exception as e:
        logger.error(f"Error processing result data: {str(e)}")
        return {
            'result': str(result_data),
            'data': [],
            'outputType': 'error'
        }


def create_error_response(error_message: str, error_details: Optional[Dict] = None) -> Dict[str, Any]:
    """
    Create standardized error response
    
    Args:
        error_message: Main error message
        error_details: Optional additional error details
        
    Returns:
        Standardized error response dictionary
    """
    response = {
        'status': 'error',
        'error': error_message,
        'data': [],
        'output': '',
        'plots': []
    }
    
    if error_details:
        response['errorDetails'] = error_details
    
    return response


def create_success_response(
    data: List[Dict] = None,
    output: str = '',
    plots: List[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Create standardized success response
    
    Args:
        data: Response data
        output: Text output
        plots: List of plot images
        **kwargs: Additional response fields
        
    Returns:
        Standardized success response dictionary
    """
    response = {
        'status': 'success',
        'data': data or [],
        'output': output,
        'plots': plots or []
    }
    
    response.update(kwargs)
    return response


def serialize_response(response_data: Dict[str, Any]) -> str:
    """
    Serialize response data to JSON string
    
    Args:
        response_data: Response dictionary to serialize
        
    Returns:
        JSON string representation
    """
    try:
        return json.dumps(response_data, cls=CustomJSONEncoder, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Error serializing response: {str(e)}")
        # Fallback to basic serialization
        fallback_response = create_error_response(f"Serialization failed: {str(e)}")
        return json.dumps(fallback_response, ensure_ascii=False)


def validate_request_data(data: Dict[str, Any], required_fields: List[str]) -> Optional[str]:
    """
    Validate request data for required fields
    
    Args:
        data: Request data dictionary
        required_fields: List of required field names
        
    Returns:
        Error message if validation fails, None if valid
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in data or data[field] is None:
            missing_fields.append(field)
    
    if missing_fields:
        return f"Missing required fields: {', '.join(missing_fields)}"
    
    return None


def extract_dataframe_info(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Extract metadata information from a DataFrame
    
    Args:
        df: Pandas DataFrame
        
    Returns:
        Dictionary with DataFrame metadata
    """
    try:
        return {
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': {col: str(dtype) for col, dtype in df.dtypes.items()},
            'memory_usage': df.memory_usage(deep=True).sum(),
            'null_counts': df.isnull().sum().to_dict()
        }
    except Exception as e:
        logger.error(f"Error extracting DataFrame info: {str(e)}")
        return {'error': str(e)}


def format_streamlit_response(app_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format Streamlit app creation response
    
    Args:
        app_info: Streamlit app information
        
    Returns:
        Formatted response for Streamlit app
    """
    try:
        if app_info.get('type') == 'error':
            return create_error_response(app_info.get('message', 'Unknown error'))
        
        return {
            'status': 'success',
            'result': app_info,
            'data': [],
            'output': app_info.get('message', 'Streamlit app created successfully'),
            'plots': []
        }
        
    except Exception as e:
        logger.error(f"Error formatting Streamlit response: {str(e)}")
        return create_error_response(f"Failed to format Streamlit response: {str(e)}")
