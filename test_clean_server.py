#!/usr/bin/env python3
"""
Test that server starts cleanly without verbose output
"""

print("🧪 Testing Clean Server Startup")
print("=" * 40)

# Test 1: Simple print (should work without debug output)
print("Hello, clean server!")

# Test 2: Import and use matplotlib (should be silent)
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 10, 50)
y = np.sin(x)

plt.figure(figsize=(8, 4))
plt.plot(x, y, 'b-', linewidth=2)
plt.title('Clean Plot Test')
plt.xlabel('X')
plt.ylabel('sin(X)')
plt.grid(True, alpha=0.3)
plt.show()

# Test 3: Try Plotly (should be silent)
try:
    import plotly.graph_objects as go
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=x, y=y, mode='lines', name='sine'))
    fig.update_layout(title='Clean Plotly Test')
    fig.show()
    
    print("✅ Plotly imported and used successfully")
except ImportError:
    print("ℹ️ Plotly not available")

# Test 4: DataFrame test
import pandas as pd

df = pd.DataFrame({
    'x': x[:10],
    'y': y[:10]
})

print("✅ DataFrame created successfully")
print(f"DataFrame shape: {df.shape}")

print("\n🎉 All tests completed!")
print("Server should start cleanly without verbose output.")
