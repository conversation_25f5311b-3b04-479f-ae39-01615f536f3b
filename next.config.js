/** @type {import('next').NextConfig} */
// Import the transpile modules config


const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,

  // Change 'hybrid' to a valid value or remove if not needed
  // Valid options are 'standalone' or 'export'
  // output: 'standalone',

  experimental: {
    // Optional: use this for additional control over server components
    serverComponentsExternalPackages: ['@prisma/client', 'bcrypt'],
    // This enables a more modern approach to handling dynamism
    serverComponentsExternalPackages: ['@tldraw/tldraw'],
  },

  webpack: (config, { isServer }) => {
    // Add your custom aliases
    config.resolve.alias.canvas = false;
    config.resolve.alias.encoding = false;

    // Handle problematic modules for both client and server
    const fallback = {
      fs: false,
      path: false,
      crypto: false,
      module: false,
      'react-native-fs': false,
      'react-native-fetch-blob': false,
      'cordova': false,
      'child_process': false,
      'net': false,
      'tls': false,
      'os': false,
      'http': false,
      'https': false,
      'stream': false,
      'zlib': false,
      'util': false,
      'url': false,
      'assert': false,
      'querystring': false,
      // Add more if needed
    };

    // Apply fallbacks for client-side
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        ...fallback
      };
    }

    // Handle worker files
    config.module.rules.push({
      test: /pdf\.worker\.(min\.)?js/,
      type: 'asset/resource',
    });

    // Add specific handling for problematic modules
    config.resolve.alias = {
      ...config.resolve.alias,
      // These modules cause issues with Next.js
      'react-native-fs': false,
      'fs': false,
      'crypto': false,
      'path': false,
      'os': false
    };

    // Add specific handling for Pinecone
    if (!isServer) {
      config.module.rules.push({
        test: /node_modules\/@pinecone-database\/pinecone/,
        use: 'null-loader'
      });
    }

    return config;
  },
  // Add CORS headers for PDF.js worker
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin',
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp',
          },
        ],
      },
    ];
  },
  serverRuntimeConfig: {
    // Will only be available on the server side
    timeoutSeconds: 60, // Increase this value as needed
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.clerk.com',
        port: '',
      },
      {
        protocol: 'https',
        hostname: 'randomuser.me',
        port: '',
      },
      {
        protocol: 'https',
        hostname: "uploadthing.com",
        port: '',
      },
      {
        protocol: 'https',
        hostname: "api.dicebear.com",
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: "utfs.io",
        port: '',
        pathname: '/**',
      },
    ],
  },
};

// Apply the transpile modules configuration
module.exports = nextConfig;