#!/usr/bin/env python3
"""
Test all fixes: input submission, DataFrame display, clean plots
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Test 1: Interactive input (should work without 400 error)
print("🧪 Testing Input Submission Fix")
name = input("Enter your name: ")
print(f"Hello, {name}!")

# Test 2: DataFrame display (should show in table tab)
print("\n📊 Testing DataFrame Display")
data = {
    'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    'Age': [25, 30, 35, 28],
    'City': ['New York', 'London', 'Tokyo', 'Paris'],
    'Salary': [50000, 60000, 70000, 55000]
}

df = pd.DataFrame(data)
print("Created DataFrame:")
print(df)

# This should display the DataFrame in the table tab
result = df
result

# Test 3: Clean plot display (should show without card styling)
print("\n📈 Testing Clean Plot Display")
plt.figure(figsize=(10, 6))

# Create sample data
x = np.linspace(0, 10, 100)
y1 = np.sin(x)
y2 = np.cos(x)

plt.plot(x, y1, 'b-', label='sin(x)', linewidth=2)
plt.plot(x, y2, 'r--', label='cos(x)', linewidth=2)
plt.title(f'Trigonometric Functions for {name}')
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

print("✅ All tests completed!")
print(f"User: {name}")
print(f"DataFrame shape: {df.shape}")
print("Check that:")
print("1. Input submission worked without 400 error")
print("2. DataFrame appears in table tab")
print("3. Plot appears without card styling")
