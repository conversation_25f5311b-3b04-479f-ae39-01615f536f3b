#!/usr/bin/env python3
"""
Test all final fixes: input submission, clean output design, no duplicates
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Test 1: Interactive input (should work without errors)
print("🧪 Testing Input Submission")
name = input("Enter your name: ")
print(f"Hello, {name}!")

age = input("Enter your age: ")
print(f"You are {age} years old!")

# Test 2: Create a plot (should appear once, no duplicates)
print("\n📈 Testing Clean Plot Display")
plt.figure(figsize=(10, 6))

x = np.linspace(0, 10, 100)
y = np.sin(x) * np.exp(-x/10)

plt.plot(x, y, 'b-', linewidth=2, label=f'Damped sine for {name}')
plt.title(f'Custom Plot for {name}')
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Test 3: Show some result data
print("\n📊 Testing Combined Output")
result_data = {
    'user': name,
    'age': age,
    'plot_type': 'damped_sine',
    'status': 'completed'
}

print("Result data:", result_data)

# Test 4: Final result (should appear in same container)
final_result = f"Test completed for {name}, age {age}"
print(final_result)

# This should all appear in one clean container without borders
final_result
