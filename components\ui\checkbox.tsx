"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

// Temporary fallback for Radix UI import issues
let CheckboxPrimitive: any
let CheckIcon: any

try {
  CheckboxPrimitive = require("@radix-ui/react-checkbox")
  CheckIcon = require("@radix-ui/react-icons").CheckIcon
} catch (error) {
  console.warn("Radix UI checkbox not available, using fallback")
  // Fallback implementation
  CheckboxPrimitive = {
    Root: "div",
    Indicator: "div"
  }
  CheckIcon = () => React.createElement("span", null, "✓")
}

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => {
  // Handle both Radix UI and fallback cases
  const RootComponent = CheckboxPrimitive.Root || "div"
  const IndicatorComponent = CheckboxPrimitive.Indicator || "div"

  return (
    <RootComponent
      ref={ref}
      className={cn(
        "peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
        className
      )}
      {...props}
    >
      <IndicatorComponent
        className={cn("flex items-center justify-center text-current")}
      >
        <CheckIcon className="h-4 w-4" />
      </IndicatorComponent>
    </RootComponent>
  )
})
Checkbox.displayName = CheckboxPrimitive.Root?.displayName || "Checkbox"

export { Checkbox }
