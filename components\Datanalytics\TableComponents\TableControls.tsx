import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Search, Plus, Loader2, GitBranch, Eye, Save, Undo2, Redo2 } from "lucide-react";

interface TableControlsProps {
  globalFilter: string;
  setGlobalFilter: (value: string) => void;
  handleAddRow: () => void;
  handleAddColumn: () => void;
  setShowVersionHistory: (show: boolean) => void;
  versions: any[];
  editHistory: any[];
  setShowChangesPreview: (show: boolean) => void;
  handleSaveChanges: () => void;
  rowOperation: {
    type: string;
    loading: boolean;
  };
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

export function TableControls({
  globalFilter,
  setGlobalFilter,
  handleAddRow,
  handleAddColumn,
  setShowVersionHistory,
  versions,
  editHistory,
  setShowChangesPreview,
  handleSaveChanges,
  rowOperation,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
}: TableControlsProps) {
  return (
    <div className="w-full overflow-hidden">
      {/* Mobile/Compact Layout */}
      <div className="flex flex-col gap-2 p-3 bg-gradient-to-r from-background/95 to-muted/20 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b sticky top-0 z-50 lg:hidden">
        {/* Top Row - Search and Save Actions */}
        <div className="flex items-center justify-between gap-2">
          {/* Search */}
          <div className="relative flex-1 max-w-[200px]">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              value={globalFilter ?? ""}
              onChange={(event) => setGlobalFilter(event.target.value)}
              className="pl-9 h-9 text-xs bg-background/50 border-border/50 focus:bg-background"
            />
          </div>

          {/* Save Actions - Always Visible */}
          <div className="flex items-center gap-2 flex-shrink-0">
            {editHistory.length > 0 && (
              <>
                <Badge variant="secondary" className="h-7 px-2 bg-amber-500/10 text-amber-700 border-amber-500/20">
                  <span className="text-xs">{editHistory.length}</span>
                </Badge>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowChangesPreview(true)}
                        className="h-9 bg-background/50 hover:bg-background border-border/50"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Preview changes</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={handleSaveChanges}
                        className="h-9 bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm"
                      >
                        <Save className="h-4 w-4 mr-1" />
                        <span className="font-medium">Save</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Save changes</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </>
            )}
          </div>
        </div>

        {/* Bottom Row - Action Buttons */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 bg-background/50 hover:bg-background border-border/50"
                    onClick={handleAddRow}
                    disabled={rowOperation.loading && rowOperation.type === 'add'}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add row</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 bg-background/50 hover:bg-background border-border/50"
                    onClick={handleAddColumn}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add column</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowVersionHistory(true)}
              className="h-8 bg-background/50 hover:bg-background border-border/50"
            >
              <GitBranch className="h-4 w-4" />
              {versions.length > 0 && (
                <Badge variant="secondary" className="ml-1 h-4 text-xs bg-primary/10 text-primary border-primary/20">
                  {versions.length}
                </Badge>
              )}
            </Button>
          </div>

          {/* Undo/Redo */}
          <div className="flex items-center gap-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onUndo}
                    disabled={!canUndo}
                    className="h-8 w-8 hover:bg-background/50"
                  >
                    <Undo2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Undo</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onRedo}
                    disabled={!canRedo}
                    className="h-8 w-8 hover:bg-background/50"
                  >
                    <Redo2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Redo</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      {/* Desktop Layout - Single Row */}
      <div className="hidden lg:flex items-center justify-between gap-3 p-3 bg-gradient-to-r from-background/95 to-muted/20 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b sticky top-0 z-50 min-h-[60px]">
        {/* Left Section - Search and Actions */}
        <div className="flex items-center gap-3 flex-1 min-w-0 overflow-hidden">
          {/* Search */}
          <div className="relative flex-shrink-0">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search data..."
              value={globalFilter ?? ""}
              onChange={(event) => setGlobalFilter(event.target.value)}
              className="pl-9 w-[180px] h-9 text-xs bg-background/50 border-border/50 focus:bg-background"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-background/50 hover:bg-background border-border/50"
                    onClick={handleAddRow}
                    disabled={rowOperation.loading && rowOperation.type === 'add'}
                  >
                    {rowOperation.loading && rowOperation.type === 'add' ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add new row</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-background/50 hover:bg-background border-border/50"
                    onClick={handleAddColumn}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add new column</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowVersionHistory(true)}
              className="h-9 bg-background/50 hover:bg-background border-border/50"
            >
              <GitBranch className="h-4 w-4" />
              {versions.length > 0 && (
                <Badge variant="secondary" className="ml-1 h-5 bg-primary/10 text-primary border-primary/20">
                  {versions.length}
                </Badge>
              )}
            </Button>
          </div>

          {/* Undo/Redo */}
          <div className="flex items-center gap-1 flex-shrink-0">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onUndo}
                    disabled={!canUndo}
                    className="h-8 w-8 hover:bg-background/50"
                  >
                    <Undo2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onRedo}
                    disabled={!canRedo}
                    className="h-8 w-8 hover:bg-background/50"
                  >
                    <Redo2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Redo (Ctrl+Shift+Z)</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Right Section - Save Actions (Always Visible) */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {editHistory.length > 0 && (
            <>
              <Badge variant="secondary" className="h-7 px-2 bg-amber-500/10 text-amber-700 border-amber-500/20">
                <span className="text-xs">{editHistory.length} change{editHistory.length !== 1 ? 's' : ''}</span>
              </Badge>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowChangesPreview(true)}
                      className="h-9 bg-background/50 hover:bg-background border-border/50"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Preview changes</TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={handleSaveChanges}
                      className="h-9 bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm font-medium"
                    >
                      <Save className="h-4 w-4 mr-1" />
                      Save
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Save all changes to dataset</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default TableControls;