/**
 * Cell Execution Hook
 * 
 * This hook manages the execution state and logic for code cells.
 * It handles:
 * - Code execution timing and state management
 * - Input/output handling for interactive code
 * - Error handling and user feedback
 * - Integration with the backend execution API
 */

import { useState, useRef, useCallback } from 'react'
import { toast } from 'sonner'

interface ExecutionState {
  isRunning: boolean
  executionTime: { startTime: Date; endTime: Date } | null
  needsInput: boolean
  inputPrompt: string
  userInput: string
  originalCode: string
}

interface UseExecutionOptions {
  onRun?: (id: string, code: string, shouldShowGraphicWalker?: boolean) => Promise<any>
  onViewModeChange?: (mode: string) => void
  cellId: string
}

export function useCellExecution({ onRun, onViewModeChange, cellId }: UseExecutionOptions) {
  const [executionState, setExecutionState] = useState<ExecutionState>({
    isRunning: false,
    executionTime: null,
    needsInput: false,
    inputPrompt: '',
    userInput: '',
    originalCode: ''
  })

  const executionTimerRef = useRef<NodeJS.Timeout | null>(null)

  const updateExecutionState = useCallback((updates: Partial<ExecutionState>) => {
    setExecutionState(prev => ({ ...prev, ...updates }))
  }, [])

  const startExecution = useCallback((code: string) => {
    updateExecutionState({
      isRunning: true,
      executionTime: null,
      originalCode: code
    })

    // Start execution timer
    const startTime = new Date()
    executionTimerRef.current = setInterval(() => {
      // Timer for UI feedback
    }, 100)

    return startTime
  }, [updateExecutionState])

  const stopExecution = useCallback((startTime: Date) => {
    const endTime = new Date()
    
    updateExecutionState({
      isRunning: false,
      executionTime: { startTime, endTime }
    })

    // Clear timer
    if (executionTimerRef.current) {
      clearInterval(executionTimerRef.current)
      executionTimerRef.current = null
    }
  }, [updateExecutionState])

  const handleInputSubmit = useCallback(async (inputValue?: string) => {
    const inputToUse = inputValue || executionState.userInput
    if (!inputToUse.trim()) return

    // Ensure we have the original code
    const codeToSubmit = executionState.originalCode
    if (!codeToSubmit.trim()) {
      toast.error('No code to execute with input')
      return
    }

    try {
      console.log('Submitting input via Next.js API route')
      console.log('Input data:', { input: inputToUse.trim(), code: codeToSubmit })
      
      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: codeToSubmit,
          language: 'python',
          datasets: [],
          user_input: inputToUse.trim()
        }),
      })

      if (response.ok) {
        const result = await response.json()

        // Clear input state
        updateExecutionState({
          userInput: '',
          needsInput: false,
          inputPrompt: ''
        })

        toast.success('Input submitted successfully')
      } else {
        const errorData = await response.json().catch(() => ({}))
        toast.error(`Failed to submit input: ${errorData.error || 'Unknown error'}`)
        updateExecutionState({
          needsInput: false,
          isRunning: false
        })
      }
    } catch (error) {
      console.error('Error submitting input:', error)
      toast.error('Error submitting input')
      updateExecutionState({
        needsInput: false,
        isRunning: false
      })
    }
  }, [executionState.userInput, executionState.originalCode, updateExecutionState])

  const executeCell = useCallback(async (
    code: string,
    shouldShowGraphicWalker: boolean = false
  ) => {
    if (executionState.isRunning || !onRun) return

    // Validation checks
    if (!code.trim()) {
      toast.error('Please enter some code to execute')
      return
    }

    const startTime = startExecution(code)

    try {
      await onRun(cellId, code, shouldShowGraphicWalker)
      toast.success('Code executed successfully')
    } catch (error) {
      console.error('Execution error:', error)
      toast.error('Execution failed')
    } finally {
      stopExecution(startTime)
    }
  }, [executionState.isRunning, onRun, cellId, startExecution, stopExecution])

  const handleInputRequirement = useCallback((needsInput: boolean, prompt?: string) => {
    updateExecutionState({
      needsInput,
      inputPrompt: prompt || 'Enter input:'
    })

    if (needsInput && onViewModeChange) {
      onViewModeChange('output')
    }
  }, [updateExecutionState, onViewModeChange])

  const clearInputState = useCallback(() => {
    updateExecutionState({
      needsInput: false,
      inputPrompt: '',
      userInput: '',
      originalCode: ''
    })
  }, [updateExecutionState])

  return {
    // State
    isRunning: executionState.isRunning,
    executionTime: executionState.executionTime,
    needsInput: executionState.needsInput,
    inputPrompt: executionState.inputPrompt,
    userInput: executionState.userInput,
    originalCode: executionState.originalCode,

    // Actions
    executeCell,
    handleInputSubmit,
    handleInputRequirement,
    clearInputState,
    updateUserInput: (input: string) => updateExecutionState({ userInput: input }),
    
    // Internal state management
    updateExecutionState
  }
}
