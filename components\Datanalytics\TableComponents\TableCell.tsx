"use client";

import { useState, useRef, useEffect } from "react";
import { Column, Row } from "@tanstack/react-table";
import { <PERSON><PERSON><PERSON>, Trash2, Copy } from "lucide-react";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { EnhancedCellWrapper } from './SmartCellContent';

// Define base data type
interface TableData {
  [key: string]: string | number | boolean;
}

// Define props interface with proper typing
interface TableCellProps<TData extends TableData> {
  row: Row<TData>;
  column: Column<TData>;
  value: any;
  columnWidth?: number;
  onEdit: (value: any) => void;
  onDeleteRow: (index: number) => void;
  onDuplicateRow: (index: number) => void;
}

// Enhanced cell value formatting for spreadsheet-like display
const formatCellValue = (value: any): string => {
  if (value === null || value === undefined || value === '') return '';
  if (typeof value === 'number') {
    // Better number formatting with proper decimal places
    if (Number.isInteger(value)) {
      return new Intl.NumberFormat('en-US').format(value);
    }
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 3
    }).format(value);
  }
  if (typeof value === 'boolean') return value ? '✓' : '✗';
  if (value instanceof Date) {
    try {
      return value.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return value.toString();
    }
  }
  return String(value).trim();
};

export function TableCell<TData extends TableData>({
  row,
  // column is required by the type but not used in this component
  column: _,
  value,
  columnWidth = 120,
  onEdit,
  onDeleteRow,
  onDuplicateRow
}: TableCellProps<TData>) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value?.toString() || '');
  const inputRef = useRef<HTMLInputElement>(null);

  // Update handleEditClick to properly handle both mouse and keyboard events
  const handleEditClick = (e?: React.MouseEvent | React.KeyboardEvent | MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Set editing state
    setIsEditing(true);

    // Use a slightly longer timeout to ensure the context menu is fully closed
    // before focusing the input element
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.select();
      }
    }, 50);
  };

  const handleSave = () => {
    if (editValue !== value?.toString()) {
      onEdit(editValue);
    }
    setIsEditing(false);
  };

  useEffect(() => {
    if (!isEditing) return;

    // Add a small delay before adding the click outside handler
    // to prevent immediate closing when opening from context menu
    const timeoutId = setTimeout(() => {
      const handleClickOutside = (e: MouseEvent) => {
        const target = e.target as Node;
        if (inputRef.current && !inputRef.current.contains(target)) {
          handleSave();
        }
      };

      document.addEventListener('mousedown', handleClickOutside);

      // Store the cleanup function to be called when the component unmounts or the effect reruns
      const cleanup = () => document.removeEventListener('mousedown', handleClickOutside);
      // Store the cleanup function on the ref so we can access it in our own cleanup
      (inputRef.current as any)._clickOutsideCleanup = cleanup;
    }, 200);

    return () => {
      clearTimeout(timeoutId);
      // Call the cleanup function if it exists
      if (inputRef.current && (inputRef.current as any)._clickOutsideCleanup) {
        (inputRef.current as any)._clickOutsideCleanup();
      }
    };
  }, [isEditing, editValue, value]);

  if (isEditing) {
    return (
      <div className="relative w-full h-full" onClick={e => e.stopPropagation()} onMouseDown={e => e.stopPropagation()}>
        <input
          ref={inputRef}
          type="text"
          value={editValue}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditValue(e.target.value)}
          onBlur={(e) => {
            // Add a small delay to prevent immediate blur when opening from context menu
            setTimeout(() => {
              // Only save if the element is still in the DOM
              if (document.contains(e.target)) {
                handleSave();
              }
            }, 100);
          }}
          onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
            e.stopPropagation();
            if (e.key === 'Enter') handleSave();
            if (e.key === 'Escape') {
              setEditValue(value?.toString() || '');
              setIsEditing(false);
            }
            if (e.key === 'Tab') {
              e.preventDefault();
              handleSave();
            }
          }}
          className="w-full h-full px-2 py-1.5 text-sm bg-background border-2 border-primary/50 rounded-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 shadow-sm resize-none"
          style={{
            width: `${Math.max(columnWidth, editValue.length * 8 + 32)}px`,
            minWidth: `${columnWidth}px`,
            height: '100%'
          }}
          placeholder="Enter value..."
          autoFocus
        />
      </div>
    );
  }

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <div className="w-full h-full" style={{ minWidth: `${columnWidth}px` }}>
          <EnhancedCellWrapper
            value={formatCellValue(value)}
            columnWidth={columnWidth}
            onDoubleClick={handleEditClick}
            className="w-full h-full"
          />
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        <ContextMenuItem
          onSelect={(e) => {
            e.preventDefault();
            // Use setTimeout to delay the edit action until after the context menu is closed
            setTimeout(() => {
              handleEditClick();
            }, 100);
          }}
        >
          <Pencil className="mr-2 h-3.5 w-3.5" />
          <span className="text-xs">Edit Cell</span>
        </ContextMenuItem>
        <ContextMenuItem onClick={() => onEdit("")}>
          <Trash2 className="mr-2 h-3.5 w-3.5" />
          <span className="text-xs">Clear Cell</span>
        </ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem onClick={() => onDeleteRow(row.index)}>
          <Trash2 className="mr-2 h-3.5 w-3.5" />
          <span className="text-xs">Delete Row</span>
        </ContextMenuItem>
        <ContextMenuItem onClick={() => onDuplicateRow(row.index)}>
          <Copy className="mr-2 h-3.5 w-3.5" />
          <span className="text-xs">Duplicate Row</span>
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}

export default TableCell;