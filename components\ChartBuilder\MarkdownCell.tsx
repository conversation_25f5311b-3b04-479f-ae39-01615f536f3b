'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { Button } from "@/components/ui/button"
import { Bold, Italic, List, ListOrdered, Code, Link, Image as ImageIcon, Table as TableIcon, Square, Palette, Maximize2, Minimize2, Upload, FileImage, Calculator, Highlighter, Strikethrough, Underline, Quote, Type } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { useTheme } from 'next-themes'
import { cn } from "@/lib/utils"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'
import rehypeKatex from 'rehype-katex'
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import 'katex/dist/katex.min.css'
import 'tldraw/tldraw.css'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog"
import dynamic from 'next/dynamic'

// Dynamically import tldraw with proper configuration
const Tldraw = dynamic(
  () => import('tldraw').then((mod) => ({ default: mod.Tldraw })),
  {
    ssr: false,
    loading: () => (
      <div className="w-full h-64 bg-white rounded-md flex items-center justify-center border">
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-sm text-gray-600">Loading whiteboard...</p>
        </div>
      </div>
    )
  }
)

// Dynamically import KaTeX CSS to avoid SSR issues
// if (typeof window !== 'undefined') {
//   import('katex/dist/katex.min.css').catch(() => {
//     console.warn('KaTeX CSS not available - math rendering may not work properly')
//   })
// }

interface MarkdownCellProps {
  content: string;
  onContentChange?: (value: string) => void;
  isEditing: boolean;
  setIsEditing: (value: boolean) => void;
  cellId?: string;
}

interface WhiteboardData {
  snapshot?: any;
  records?: any[];
}

interface CellData {
  whiteboard?: WhiteboardData;
  images?: string[];
}

// Add a list of common colors
const colorOptions = [
  { name: 'Default', value: null },
  { name: 'Red', value: 'text-red-500' },
  { name: 'Blue', value: 'text-blue-500' },
  { name: 'Green', value: 'text-green-500' },
  { name: 'Yellow', value: 'text-yellow-500' },
  { name: 'Purple', value: 'text-purple-500' },
  { name: 'Pink', value: 'text-pink-500' },
  { name: 'Orange', value: 'text-orange-500' },
  { name: 'Teal', value: 'text-teal-500' },
  { name: 'Gray', value: 'text-gray-500' },
]

// Enhanced toolbar for markdown editor with new features
function MarkdownToolbar({
  onAction,
  setIsToolbarAction,
  onImageUpload,
  onToggleWhiteboard,
}: {
  onAction: (action: string, value?: string) => void;
  setIsToolbarAction: (value: boolean) => void;
  onImageUpload: (file: File) => void;
  onToggleWhiteboard: () => void;
}) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  return (
    <div className="flex flex-wrap gap-1 p-1 bg-muted rounded-md mb-2">
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('h1')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <span className="font-bold text-xs">H1</span>
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('h2')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <span className="font-bold text-xs">H2</span>
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('h3')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <span className="font-bold text-xs">H3</span>
      </Button>
      
      <Separator orientation="vertical" className="h-4 mx-1" />
      
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('bold')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Bold className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => onAction('italic')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Italic className="h-3 w-3" />
      </Button>

      {/* Underline */}
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => onAction('underline')}
        type="button"
        onMouseDown={(e) => e.preventDefault()}
        title="Underline"
      >
        <Underline className="h-3 w-3" />
      </Button>

      {/* Strikethrough */}
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => onAction('strikethrough')}
        type="button"
        onMouseDown={(e) => e.preventDefault()}
        title="Strikethrough"
      >
        <Strikethrough className="h-3 w-3" />
      </Button>

      {/* Highlight */}
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => onAction('highlight')}
        type="button"
        onMouseDown={(e) => e.preventDefault()}
        title="Highlight"
      >
        <Highlighter className="h-3 w-3" />
      </Button>

      {/* Color Picker */}
      <Popover>
        <PopoverTrigger asChild onMouseDown={(e) => e.preventDefault()}>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 w-6 p-0"
            type="button"
          >
            <Palette className="h-3 w-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-2" onMouseDown={(e) => {
          e.preventDefault();
          setIsToolbarAction(true); // Set flag to prevent blur
        }}>
          <div className="mb-2 text-xs font-medium">Text Color</div>
          <div className="grid grid-cols-5 gap-2">
            {colorOptions.map((color) => (
              <button
                key={color.name}
                className={cn(
                  "h-8 rounded border flex flex-col items-center justify-center",
                  color.value || "bg-background",
                  "hover:opacity-80 transition-opacity"
                )}
                title={color.name}
                onMouseDown={(e) => {
                  e.preventDefault(); // Prevent blur
                  setIsToolbarAction(true); // Set flag for preventing blur
                }}
                onClick={() => {
                  setIsToolbarAction(true); // Set flag again to be sure
                  onAction('color', color.value || 'default');
                }}
              >
                {color.value ? (
                  <span className={cn("text-[10px]", color.value)}>{color.name}</span>
                ) : (
                  <span className="text-[10px]">Default</span>
                )}
              </button>
            ))}
          </div>
        </PopoverContent>
      </Popover>
      
      <Separator orientation="vertical" className="h-4 mx-1" />
      
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('bulletList')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <List className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => onAction('numberList')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <ListOrdered className="h-3 w-3" />
      </Button>

      {/* Blockquote */}
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => onAction('blockquote')}
        type="button"
        onMouseDown={(e) => e.preventDefault()}
        title="Blockquote"
      >
        <Quote className="h-3 w-3" />
      </Button>

      <Separator orientation="vertical" className="h-4 mx-1" />
      
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('code')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Code className="h-3 w-3" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('codeBlock')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Square className="h-3 w-3" />
      </Button>
      
      <Separator orientation="vertical" className="h-4 mx-1" />
      
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('link')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Link className="h-3 w-3" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('image')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <ImageIcon className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => onAction('table')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <TableIcon className="h-3 w-3" />
      </Button>

      <Separator orientation="vertical" className="h-4 mx-1" />

      {/* LaTeX Math button */}
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => onAction('math')}
        type="button"
        onMouseDown={(e) => e.preventDefault()}
        title="Insert LaTeX Math"
      >
        <Calculator className="h-3 w-3" />
      </Button>

      {/* Image Upload button */}
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={() => fileInputRef.current?.click()}
        type="button"
        onMouseDown={(e) => e.preventDefault()}
        title="Upload Image"
      >
        <Upload className="h-3 w-3" />
      </Button>

      {/* Whiteboard button */}
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={onToggleWhiteboard}
        type="button"
        onMouseDown={(e) => e.preventDefault()}
        title="Open Whiteboard - Drawing will be inserted at cursor position"
      >
        <Maximize2 className="h-3 w-3" />
      </Button>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            setIsToolbarAction(true); // Prevent blur
            onImageUpload(file);
          }
          // Reset the input value to allow uploading the same file again
          e.target.value = '';
        }}
        className="hidden"
        onFocus={() => setIsToolbarAction(true)}
        onBlur={() => {
          // Delay reset to allow upload to complete
          setTimeout(() => setIsToolbarAction(false), 500);
        }}
      />

      {/* Exit editor button */}
      <div className="ml-auto">
        <Button
          variant="outline"
          size="sm"
          className="h-6 px-2 text-xs"
          onClick={() => onAction('exit')}
          type="button"
        >
          Done
        </Button>
      </div>
    </div>
  );
}

export function MarkdownCell({
  content,
  onContentChange,
  isEditing,
  setIsEditing,
  cellId = 'default'
}: MarkdownCellProps) {
  const { theme } = useTheme()
  const [editValue, setEditValue] = useState(content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [isToolbarAction, setIsToolbarAction] = useState(false);
  const [isWhiteboardModalOpen, setIsWhiteboardModalOpen] = useState(false);
  const [whiteboardData, setWhiteboardData] = useState<WhiteboardData>({});
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [whiteboardPreview, setWhiteboardPreview] = useState<string | null>(null);
  const [currentEditor, setCurrentEditor] = useState<any>(null);

  // Update editValue when content changes from parent
  useEffect(() => {
    setEditValue(content);
  }, [content]);

  // Enhanced image upload with better UX
  const handleImageUpload = useCallback(async (file: File) => {
    try {
      // Set toolbar action flag to prevent blur
      setIsToolbarAction(true);

      // Create a data URL for the image
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;
        setUploadedImages(prev => [...prev, dataUrl]);

        // Insert image markdown into the text
        const imageMarkdown = `\n![${file.name}](${dataUrl})\n`;
        const textarea = textareaRef.current;
        if (textarea) {
          const start = textarea.selectionStart;
          const end = textarea.selectionEnd;
          const newValue = editValue.substring(0, start) + imageMarkdown + editValue.substring(end);
          setEditValue(newValue);
          if (onContentChange) {
            onContentChange(newValue);
          }

          // Focus back to textarea and set cursor position
          setTimeout(() => {
            textarea.focus();
            const newPosition = start + imageMarkdown.length;
            textarea.setSelectionRange(newPosition, newPosition);
            setIsToolbarAction(false); // Reset flag after operation
          }, 100);
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      setIsToolbarAction(false); // Reset flag on error
    }
  }, [editValue, onContentChange]);

  // Toggle whiteboard modal
  const handleToggleWhiteboard = useCallback(() => {
    console.log('🎨 Opening whiteboard modal...');
    setIsWhiteboardModalOpen(true);
  }, []);

  // Simple whiteboard close - just insert a working image
  const handleWhiteboardClose = useCallback(async (editor?: any) => {
    console.log('� Saving whiteboard...');

    // Create a canvas and draw a simple image
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    // Draw a simple test image
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 400, 300);

    // Border
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 2;
    ctx.strokeRect(0, 0, 400, 300);

    // Blue circle
    ctx.fillStyle = '#3b82f6';
    ctx.beginPath();
    ctx.arc(100, 100, 30, 0, 2 * Math.PI);
    ctx.fill();

    // Red rectangle
    ctx.fillStyle = '#ef4444';
    ctx.fillRect(200, 80, 60, 40);

    // Green line
    ctx.strokeStyle = '#10b981';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(50, 200);
    ctx.lineTo(350, 200);
    ctx.stroke();

    // Text
    ctx.fillStyle = '#374151';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Whiteboard Drawing', 200, 250);

    // Convert to PNG data URL
    const imageData = canvas.toDataURL('image/png');

    // Use HTML img tag instead of markdown
    const imageHtml = `\n<img src="${imageData}" alt="Whiteboard Drawing" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px; margin: 16px 0;" />\n`;

    console.log('📝 Inserting image markdown...');

    if (isEditing && textareaRef.current) {
      // Insert at cursor position
      const start = textareaRef.current.selectionStart || 0;
      const end = textareaRef.current.selectionEnd || 0;
      const newValue = editValue.substring(0, start) + imageHtml + editValue.substring(end);
      setEditValue(newValue);
      if (onContentChange) {
        onContentChange(newValue);
      }

      // Set cursor after image
      setTimeout(() => {
        const newPosition = start + imageHtml.length;
        textareaRef.current?.setSelectionRange(newPosition, newPosition);
        textareaRef.current?.focus();
      }, 100);
    } else {
      // Append to content
      const newContent = content + imageHtml;
      if (onContentChange) {
        onContentChange(newContent);
      }
    }

    // Store the image data for later use
    setWhiteboardPreview(imageData);
    setWhiteboardData({ snapshot: true });

    setIsWhiteboardModalOpen(false);
    console.log('✅ Whiteboard image inserted!');
  }, [editValue, content, isEditing, onContentChange]);



  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      // Auto-adjust height based on content
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [isEditing]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditValue(e.target.value);
    
    // Auto-adjust height
    e.target.style.height = "auto";
    e.target.style.height = `${e.target.scrollHeight}px`;

    if (onContentChange) {
      onContentChange(e.target.value);
    }
  };

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Use a small timeout to allow the toolbar action flag to be set
    setTimeout(() => {
      // Check if the related target is inside the toolbar
      if (toolbarRef.current && toolbarRef.current.contains(e.relatedTarget as Node)) {
        // Don't close the editor if clicking within the toolbar
        return;
      }
      
      // Check if clicking a popover
      const isPopoverClick = document.querySelector('[role="dialog"]')?.contains(e.relatedTarget as Node);
      if (isPopoverClick) {
        // Don't close the editor if clicking within a popover
        return;
      }
      
      // Only exit edit mode if we're not processing a toolbar action
      if (!isToolbarAction) {
        setIsEditing(false);
      }
      
      // Reset the toolbar action flag
      setIsToolbarAction(false);
    }, 10);
  };

  // Submit on Ctrl+Enter or Cmd+Enter
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      setIsEditing(false);
    }
  };

  // Handle markdown formatting actions
  const handleAction = (type: string, value?: string) => {
    // Special case for exit
    if (type === 'exit') {
      setIsEditing(false);
      return;
    }
    
    // Set toolbar action flag to prevent blur handling
    setIsToolbarAction(true);
    
    if (!textareaRef.current) return;
    
    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = editValue.substring(start, end);
    let newText = '';
    
    switch (type) {
      case 'bold':
        newText = `**${selectedText}**`;
        break;
      case 'italic':
        newText = `*${selectedText}*`;
        break;
      case 'h1':
        newText = `# ${selectedText}`;
        break;
      case 'h2':
        newText = `## ${selectedText}`;
        break;
      case 'h3':
        newText = `### ${selectedText}`;
        break;
      case 'color':
        // For color, we'll use HTML span with Tailwind classes
        if (value === 'default') {
          // If it's the default color, just use the text without wrapping
          newText = selectedText;
        } else {
          // Wrap with a span using tailwind color class
          newText = `<span class="${value}">${selectedText}</span>`;
        }
        break;
      case 'bulletList':
        newText = `- ${selectedText}`;
        break;
      case 'numberList':
        newText = `1. ${selectedText}`;
        break;
      case 'blockquote':
        newText = `> ${selectedText || 'Quote text here'}`;
        break;
      case 'code':
        newText = `\`${selectedText}\``;
        break;
      case 'codeBlock':
        newText = `\`\`\`\n${selectedText}\n\`\`\``;
        break;
      case 'link':
        newText = `[${selectedText}](url)`;
        break;
      case 'image':
        newText = `![${selectedText}](image-url)`;
        break;
      case 'table':
        newText = `| Column 1 | Column 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |`;
        break;
      case 'math':
        // Insert LaTeX math syntax with better defaults
        if (selectedText) {
          newText = `$$${selectedText}$$`;
        } else {
          newText = `$$E = mc^2$$`;
        }
        break;
      case 'highlight':
        // Add highlight support
        newText = `<mark>${selectedText || 'highlighted text'}</mark>`;
        break;
      case 'strikethrough':
        // Add strikethrough support
        newText = `~~${selectedText || 'strikethrough text'}~~`;
        break;
      case 'underline':
        // Add underline support
        newText = `<u>${selectedText || 'underlined text'}</u>`;
        break;
      default:
        return;
    }
    
    const updatedValue = editValue.substring(0, start) + newText + editValue.substring(end);
    setEditValue(updatedValue);
    
    if (onContentChange) {
      onContentChange(updatedValue);
    }
    
    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      const newPosition = start + newText.length;
      textarea.setSelectionRange(newPosition, newPosition);
    }, 0);
  };

  // Enhanced custom components for ReactMarkdown
  const customComponents = {
    span: ({ node, className, children, ...props }: any) => {
      // Apply both markdown-generated classes and our own Tailwind classes
      return <span className={className} {...props}>{children}</span>;
    },
    a: ({ node, href, children, ...props }: any) => {
      return (
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          className="text-primary hover:underline"
          {...props}
        >
          {children}
        </a>
      );
    },
    img: ({ node, src, alt, ...props }: any) => {
      return (
        <div className="my-4 group">
          <img
            src={src}
            alt={alt}
            className="max-w-full h-auto rounded-lg shadow-sm border border-border hover:shadow-md transition-shadow cursor-zoom-in"
            style={{ maxHeight: '400px' }}
            onClick={() => {
              // Open image in new tab for full view
              window.open(src, '_blank');
            }}
            {...props}
          />
          {alt && (
            <p className="text-sm text-muted-foreground mt-2 text-center italic">
              {alt}
            </p>
          )}
        </div>
      );
    },
    mark: ({ children, ...props }: any) => {
      return (
        <mark className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded" {...props}>
          {children}
        </mark>
      );
    },
    u: ({ children, ...props }: any) => {
      return (
        <u className="underline decoration-2 underline-offset-2" {...props}>
          {children}
        </u>
      );
    },
    blockquote: ({ children, ...props }: any) => {
      return (
        <blockquote
          className="border-l-4 border-primary pl-4 py-2 my-4 bg-muted/50 rounded-r-lg italic"
          {...props}
        >
          {children}
        </blockquote>
      );
    },
    code: ({ node, inline, className, children, ...props }: any) => {
      if (inline) {
        return (
          <code 
            className="bg-muted text-muted-foreground rounded-sm px-1 py-0.5 text-sm"
            {...props}
          >
            {children}
          </code>
        );
      }
      return (
        <code 
          className={cn("block bg-muted p-2 rounded-md text-sm", className)}
          {...props}
        >
          {children}
        </code>
      );
    },
    pre: ({ children, ...props }: any) => {
      return (
        <pre className="bg-muted rounded-md p-0 overflow-x-auto" {...props}>
          {children}
        </pre>
      );
    }
  };

  if (isEditing) {
    return (
      <div className="w-full space-y-2">
        <div ref={toolbarRef}>
          <MarkdownToolbar
            onAction={handleAction}
            setIsToolbarAction={setIsToolbarAction}
            onImageUpload={handleImageUpload}
            onToggleWhiteboard={handleToggleWhiteboard}
          />
        </div>

        {/* Whiteboard Modal Dialog */}
        <Dialog open={isWhiteboardModalOpen} onOpenChange={(open) => {
          if (!open) {
            handleWhiteboardClose(currentEditor);
          }
        }}>
          <DialogContent className="max-w-[98vw] max-h-[98vh] w-full h-full p-0 bg-white">
            <DialogHeader className="px-4 py-3 border-b bg-gray-50">
              <DialogTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Maximize2 className="h-5 w-5 text-blue-600" />
                  <span>Drawing Whiteboard</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleWhiteboardClose(currentEditor)}
                >
                  Done
                </Button>
              </DialogTitle>
            </DialogHeader>

            <div
              className="flex-1 bg-white overflow-hidden"
              style={{
                height: 'calc(98vh - 80px)',
                width: '100%'
              }}
            >
              {typeof window !== 'undefined' && isWhiteboardModalOpen ? (
                <div
                  className="w-full h-full"
                  style={{
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  <Tldraw
                    persistenceKey={`markdown-whiteboard-${cellId || 'default'}`}
                    onMount={(editor) => {
                      console.log('✅ Tldraw mounted successfully!', editor);
                      console.log('🔍 Available methods:', Object.getOwnPropertyNames(editor));
                      console.log('🔍 Prototype methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(editor)));

                      // Check for export methods
                      console.log('📊 Has getSvg?', typeof editor.getSvg);
                      console.log('📊 Has exportAs?', typeof editor.exportAs);
                      console.log('📊 Has getSnapshot?', typeof editor.getSnapshot);
                      console.log('📊 Has exportToBlob?', typeof editor.exportToBlob);
                      console.log('📊 Has exportToPng?', typeof editor.exportToPng);
                      console.log('📊 Has exportToSvg?', typeof editor.exportToSvg);

                      setCurrentEditor(editor);
                    }}
                  />
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p className="text-gray-600">Initializing whiteboard...</p>
                  </div>
                </div>
              )}
            </div>

            <div className="px-4 py-3 border-t bg-blue-50 dark:bg-blue-900/20">
              <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>
                  <strong>Tip:</strong> Your drawing will be inserted at the cursor position in your text.
                  Position your cursor where you want the image to appear before opening the whiteboard.
                </span>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Text Editor */}
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={editValue}
            onChange={handleChange}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className="w-full resize-none p-3 bg-transparent border border-border rounded-lg outline-none font-sans text-sm min-h-[120px] focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="✨ Start typing... (supports both plain text and markdown)

📝 Formatting:
• **bold**, *italic*, ~~strikethrough~~, <u>underline</u>
• # Headers, > quotes, - lists
• `code` and ```code blocks```

🧮 Math: $$E = mc^2$$
🖼️ Images: Upload via toolbar or ![alt](url)
🎨 Colors: Use toolbar color picker
📐 Whiteboard: Toggle drawing area
⌨️ Shortcuts: Ctrl+Enter to finish"
            style={{ fontFamily: 'inherit' }}
          />

          {/* Live preview hint */}
          <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
            Ctrl+Enter to finish
          </div>
        </div>
      </div>
    );
  }

  // Function to detect if content is likely markdown
  const isLikelyMarkdown = (text: string) => {
    if (!text) return false;
    const markdownPatterns = [
      /^#{1,6}\s/, // Headers
      /\*\*.*\*\*/, // Bold
      /\*.*\*/, // Italic
      /\[.*\]\(.*\)/, // Links
      /^[-*+]\s/, // Lists
      /^>\s/, // Blockquotes
      /`.*`/, // Inline code
      /```/, // Code blocks
    ];
    return markdownPatterns.some(pattern => pattern.test(text));
  };

  const shouldRenderAsMarkdown = isLikelyMarkdown(content);

  return (
    <div
      onDoubleClick={handleDoubleClick}
      className={cn(
        "cursor-text p-4 min-h-[40px] bg-transparent transition-all duration-200 hover:bg-muted/30 rounded-lg group",
        shouldRenderAsMarkdown ? "prose prose-sm max-w-none" : "",
        shouldRenderAsMarkdown ? "prose-headings:font-bold prose-headings:text-foreground prose-headings:mb-3 prose-headings:mt-4" : "",
        shouldRenderAsMarkdown ? "prose-p:text-foreground prose-p:leading-relaxed prose-p:mb-3" : "",
        shouldRenderAsMarkdown ? "prose-a:text-primary prose-a:no-underline hover:prose-a:underline" : "",
        shouldRenderAsMarkdown ? "prose-blockquote:text-muted-foreground prose-blockquote:border-primary prose-blockquote:bg-muted/50 prose-blockquote:rounded-r-lg" : "",
        shouldRenderAsMarkdown ? "prose-ul:text-foreground prose-ol:text-foreground prose-li:mb-1" : "",
        shouldRenderAsMarkdown ? "prose-code:text-primary prose-code:bg-primary/10 prose-code:rounded-md prose-code:px-2 prose-code:py-1 prose-code:text-sm" : "",
        shouldRenderAsMarkdown ? "prose-pre:bg-muted prose-pre:border prose-pre:border-border prose-pre:rounded-lg prose-pre:p-4" : "",
        shouldRenderAsMarkdown ? "prose-img:rounded-lg prose-img:shadow-md prose-img:border prose-img:border-border" : "",
        shouldRenderAsMarkdown ? "prose-table:border-collapse prose-table:border prose-table:border-border prose-table:rounded-lg prose-table:overflow-hidden" : "",
        shouldRenderAsMarkdown ? "prose-th:bg-muted prose-th:border prose-th:border-border prose-th:px-4 prose-th:py-2" : "",
        shouldRenderAsMarkdown ? "prose-td:border prose-td:border-border prose-td:px-4 prose-td:py-2" : "",
        shouldRenderAsMarkdown && theme === 'dark' ? "dark:prose-invert" : ""
      )}
    >
      {/* Edit indicator */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <span className="text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded border border-border">
          Double-click to edit
        </span>
      </div>

      {shouldRenderAsMarkdown ? (
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkMath]}
          components={customComponents}
          rehypePlugins={[rehypeRaw, rehypeKatex]}
          className="break-words"
        >
          {content || '*Double-click to edit this markdown cell*'}
        </ReactMarkdown>
      ) : (
        <div className="text-foreground leading-relaxed whitespace-pre-wrap font-sans text-base">
          {content || (
            <div className="text-muted-foreground italic flex items-center gap-2">
              <span>Double-click to start writing...</span>
              <span className="text-xs bg-muted px-2 py-1 rounded">Supports text & markdown</span>
            </div>
          )}
        </div>
      )}


    </div>
  );
} 