"use client"

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useAuth, useUser, UserButton } from "@clerk/nextjs";
import {
  ChevronLeft,
  ChevronRight,
  ChartPie,
  Users,
  FileTextIcon,
  FolderKanban,
  NotepadText,
  Settings,
  LifeBuoy,
  Bell,
  Activity,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MdSpaceDashboard } from "react-icons/md";
import { FaChartSimple } from "react-icons/fa6";
import { FaTableList } from "react-icons/fa6";
import { getRecentNotifications } from "@/actions/chatActions";
import { formatDistanceToNow } from "date-fns";
import { toast } from "sonner";
import { BsChatLeftTextFill } from "react-icons/bs";
import { FaBrain } from "react-icons/fa";
import { RiChatSmile2Line } from "react-icons/ri";
import { ModeToggle } from "../ThemeToggle";

const mainRoutes = [
  { path: "/hr/workspace", name: "Workspace", icon: MdSpaceDashboard },
  { path: "/hr/dashboard", name: "Dashboard", icon: FaChartSimple },
  { path: "/hr/dataeditor", name: "Data Editor", icon: FaTableList },
  { path: "/hr/pmagent", name: "AI", icon: FaBrain },
  { path: "/hr/bot", name: "Chatbot", icon: RiChatSmile2Line },
  { path: "/hr/monitoring", name: "Server Monitor", icon: Activity },
  // { path: "/hr/help", name: "Help & Support", icon: LifeBuoy },
];

interface Notification {
  id: string;
  title: string;
  message: string;
  time: Date;
  channelId: string;
}

const MainSidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    const fetchNotifications = async () => {
      const result = await getRecentNotifications();
      if (result.success) {
        setNotifications(result.notifications);
      }
    };

    if (isSignedIn) {
      fetchNotifications();
      // Refresh notifications every minute
      const interval = setInterval(fetchNotifications, 60000);
      return () => clearInterval(interval);
    }
  }, [isSignedIn]);

  const handleNotificationClick = (notification: Notification) => {
    router.push(`/hr/workspace/chats/${notification.channelId}`);
    toast.success("Navigating to message");
  };

  return (
    <aside
      className={`fixed top-0 left-0 h-screen border-r bg-gray-50 dark:bg-black transition-all duration-300 flex flex-col overflow-y-auto z-20 ${
        isCollapsed ? "w-16" : "w-64"
      }`}
    >
      <div className="flex items-center justify-between p-4">
        {!isCollapsed && <h1 className="text-xl font-bold">LoopFlow</h1>}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="hover:text-white"
        >
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </Button>
      </div>

      <nav className="flex-grow">
        <ul className="space-y-2 py-4">
          {mainRoutes.map(({ path, name, icon: Icon }) => {
            const isActive = pathname === path;
            return (
              <li key={path}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        className={`flex items-center py-2 px-4 rounded-md mx-2 transition-colors
                          ${isActive 
                            ? '' 
                            : 'hover:bg-zinc-800/50 hover:text-white'
                          }`}
                        href={path}
                      >
                        <Icon className={`w-5 h-5 ${isActive ? '' : ''}`} />
                        {!isCollapsed && (
                          <span className={`ml-3 ${isActive ? 'font-medium' : ''}`}>
                            {name}
                          </span>
                        )}
                      </Link>
                    </TooltipTrigger>
                    {isCollapsed && (
                      <TooltipContent side="right">
                        <p>{name}</p>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              </li>
            );
          })}
          
          {/* Notifications below the main routes */}
          <li>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="py-2 px-4 mx-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="relative hover:text-white w-full flex justify-start"
                        >
                          <BsChatLeftTextFill className="h-5 w-5" />
                          {!isCollapsed && <span className="ml-3">Notifications</span>}
                          {notifications.length > 0 && (
                            <span className="absolute -top-1 left-4 h-4 w-4 rounded-full bg-red-500 text-xs dark:text-white flex items-center justify-center">
                              {notifications.length}
                            </span>
                          )}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[300px]">
                        <DropdownMenuLabel>Recent Messages</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {notifications.length === 0 ? (
                          <DropdownMenuItem>
                            <div className="text-sm text-muted-foreground">
                              No new messages
                            </div>
                          </DropdownMenuItem>
                        ) : (
                          notifications.map((notification) => (
                            <DropdownMenuItem 
                              key={notification.id}
                              onClick={() => handleNotificationClick(notification)}
                              className="cursor-pointer"
                            >
                              <div className="flex flex-col space-y-1">
                                <span className="font-medium">{notification.title}</span>
                                <span className="text-sm text-muted-foreground">
                                  {notification.message}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(notification.time), { addSuffix: true })}
                                </span>
                              </div>
                            </DropdownMenuItem>
                          ))
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TooltipTrigger>
                {isCollapsed && (
                  <TooltipContent side="right">
                    <p>Notifications</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </li>
        </ul>
      </nav>

      <div className="px-4 py-2 space-y-2">
        <div className="flex items-center justify-center gap-2">
          <ModeToggle />
        </div>
      </div>

      <div className="mt-auto p-4">
        {!isLoaded ? (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white/50 mx-auto"></div>
        ) : isSignedIn ? (
          <div className="flex items-center">
            <UserButton afterSignOutUrl="/" />
            {!isCollapsed && (
              <span className="ml-3 text-sm">{user?.firstName || user?.username}</span>
            )}
          </div>
        ) : (
          <Link href="/sign-in" className="text-sm hover:underline">
            Sign in
          </Link>
        )}
      </div>
    </aside>
  );
};

export default MainSidebar;