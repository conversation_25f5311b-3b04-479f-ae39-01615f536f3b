#!/usr/bin/env python3
"""
Test script for DataFrame display functionality
"""

import pandas as pd
import numpy as np

# Test 1: Direct DataFrame assignment
df = pd.DataFrame({
    'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    'Age': [25, 30, 35, 28],
    'City': ['New York', 'London', 'Tokyo', 'Paris'],
    'Salary': [50000, 60000, 70000, 55000]
})

print("DataFrame created successfully!")

# Test 2: Variable assignment that should show in table
result = df

# Test 3: DataFrame operations
filtered_data = df[df['Age'] > 27]

# Test 4: DataFrame with calculations
df['Age_Next_Year'] = df['Age'] + 1

print(f"DataFrame shape: {df.shape}")
print("DataFrame operations completed!")
