'use client'

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Upload,
  Database,
  Cloud,
  FileSpreadsheet,
  Workflow,
  BarChart3,
  Zap,
  Plus,
  ArrowRight,
  Sparkles,
  FolderPlus
} from 'lucide-react';
import FileUpload from './FileImport';
import { UploadSection } from './UploadSection';
import { toast } from 'sonner';

interface IntegrationCardProps {
  onDataLoaded?: (data: any[], headers: string[], fileName: string) => void;
  savedDatasets?: any[];
  storageInfo?: {
    used: number;
    total: number;
    percentage: number;
  };
  isLoadingDatasets?: boolean;
  onDatasetSelect?: (dataset: any) => void;
  onDeleteDataset?: (datasetId: string) => void;
  onShowVersionHistory?: (dataset: any) => void;
  allVersions?: Record<string, any[]>;
  renderDatasetInfo?: () => React.ReactNode;
}

// Integration source data with icons and colors
const integrations = [
  {
    name: 'Snowflake',
    icon: Database,
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-500/10',
    textColor: 'text-blue-600',
    description: 'Cloud data warehouse'
  },
  {
    name: 'Apache Airflow',
    icon: Workflow,
    color: 'from-emerald-500 to-teal-500',
    bgColor: 'bg-emerald-500/10',
    textColor: 'text-emerald-600',
    description: 'Workflow orchestration'
  },
  {
    name: 'Google Sheets',
    icon: FileSpreadsheet,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-500/10',
    textColor: 'text-green-600',
    description: 'Collaborative spreadsheets'
  },
  {
    name: 'BigQuery',
    icon: BarChart3,
    color: 'from-orange-500 to-red-500',
    bgColor: 'bg-orange-500/10',
    textColor: 'text-orange-600',
    description: 'Analytics data warehouse'
  },
  {
    name: 'Cloud Storage',
    icon: Cloud,
    color: 'from-purple-500 to-violet-500',
    bgColor: 'bg-purple-500/10',
    textColor: 'text-purple-600',
    description: 'File storage services'
  },
  {
    name: 'More Sources',
    icon: Plus,
    color: 'from-gray-400 to-gray-600',
    bgColor: 'bg-gray-500/10',
    textColor: 'text-gray-600',
    description: 'Additional integrations'
  }
];

export const IntegrationCard: React.FC<IntegrationCardProps> = ({
  onDataLoaded,
  savedDatasets = [],
  storageInfo = { used: 0, total: 100, percentage: 0 },
  isLoadingDatasets = false,
  onDatasetSelect = () => {},
  onDeleteDataset = () => {},
  onShowVersionHistory = () => {},
  allVersions = {},
  renderDatasetInfo = () => null
}) => {
  const [showUploadSection, setShowUploadSection] = useState(false);
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<string>('');

  const handleIntegrationClick = (integrationName: string) => {
    setSelectedIntegration(integrationName);
    setShowComingSoon(true);
    toast.info(`${integrationName} integration coming soon!`, {
      description: "We're working hard to bring you this integration.",
      duration: 3000,
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mb-4"
    >
      {/* Enhanced Curved Integration Card - Full Width */}
      <Card
        className="border border-dashed hover:border-primary/50 hover:bg-accent/30 transition-all duration-200 group w-full"
        style={{
          borderRadius: '1rem 1rem 1rem 2.5rem' // Curved bottom-left
        }}
      >
        <CardContent className="p-5">
          {/* Top Section - Main Import Action */}
          <div
            className="flex items-center justify-between cursor-pointer group/import"
            onClick={() => setShowUploadSection(true)}
          >
            <div className="flex items-center gap-4">
              <div className="p-4 bg-primary/10 rounded-xl group-hover/import:bg-primary/20 transition-colors">
                <Upload className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">Import Your Data</h3>
                <p className="text-sm text-muted-foreground">Upload files or connect to data sources</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="hidden sm:flex flex-col items-end gap-1">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-muted-foreground">6+ Sources</span>
                </div>
                <div className="flex items-center gap-1">
                  <Zap className="h-3 w-3 text-amber-500" />
                  <span className="text-xs text-muted-foreground">Real-time</span>
                </div>
              </div>
              <ArrowRight className="h-5 w-5 text-muted-foreground group-hover/import:text-primary transition-colors" />
            </div>
          </div>

          {/* Integration Sources Section */}
          <div className="mt-4 pt-4 border-t border-border/50">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-sm text-muted-foreground">Available Integrations</h4>
              <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20 text-xs">
                <Sparkles className="h-3 w-3 mr-1" />
                Coming Soon
              </Badge>
            </div>

            {/* Integration Icons Grid - Full Width */}
            <div className="grid grid-cols-3 sm:grid-cols-6 gap-3">
              {integrations.map((integration, index) => (
                <motion.div
                  key={integration.name}
                  initial={{ opacity: 0, scale: 0.8, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.4 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="group/icon cursor-pointer relative"
                  onClick={() => handleIntegrationClick(integration.name)}
                >
                  <div className={`
                    relative p-4 rounded-xl transition-all duration-300
                    ${integration.bgColor}
                    hover:shadow-lg hover:shadow-current/20
                    border border-transparent hover:border-current/30
                    backdrop-blur-sm
                  `}>
                    <div className="flex flex-col items-center gap-2">
                      <integration.icon className={`h-6 w-6 ${integration.textColor}`} />
                      <span className="text-xs font-semibold text-center leading-tight">
                        {integration.name}
                      </span>
                    </div>

                    {/* Coming Soon Badge */}
                    <div className="absolute -top-1 -right-1 opacity-0 group-hover/icon:opacity-100 transition-opacity duration-200">
                      <div className="bg-amber-500 text-white text-xs rounded-full px-1.5 py-0.5 font-medium shadow-sm">
                        Soon
                      </div>
                    </div>

                    {/* Enhanced Tooltip */}
                    <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 opacity-0 group-hover/icon:opacity-100 transition-all duration-300 pointer-events-none z-20">
                      <div className="bg-popover/95 backdrop-blur-sm text-popover-foreground text-xs rounded-xl px-3 py-2 whitespace-nowrap shadow-xl border border-border/50">
                        <div className="font-medium">{integration.name}</div>
                        <div className="text-muted-foreground">{integration.description}</div>
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover/95"></div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Section Modal */}
      {showUploadSection && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={() => setShowUploadSection(false)}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 20 }}
            className="bg-background rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between p-6 border-b">
              <div>
                <h3 className="text-xl font-semibold">Import Your Data</h3>
                <p className="text-sm text-muted-foreground">Upload files, connect databases, or browse existing datasets</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUploadSection(false)}
                className="rounded-full"
              >
                ✕
              </Button>
            </div>

            <div className="overflow-auto max-h-[calc(90vh-120px)]">
              <UploadSection
                onDataLoaded={(data, headers, fileName) => {
                  if (onDataLoaded) {
                    onDataLoaded(data, headers, fileName);
                  }
                  setShowUploadSection(false);
                }}
                savedDatasets={savedDatasets}
                storageInfo={storageInfo}
                isLoadingDatasets={isLoadingDatasets}
                onDatasetSelect={(dataset) => {
                  onDatasetSelect(dataset);
                  setShowUploadSection(false);
                }}
                onDeleteDataset={onDeleteDataset}
                onShowVersionHistory={onShowVersionHistory}
                allVersions={allVersions}
                renderDatasetInfo={renderDatasetInfo}
              />
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Coming Soon Modal */}
      {showComingSoon && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={() => setShowComingSoon(false)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-background rounded-xl p-6 max-w-md w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="text-center space-y-4">
              <div className="p-4 bg-amber-500/10 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                <Sparkles className="h-8 w-8 text-amber-500" />
              </div>

              <div>
                <h3 className="text-lg font-semibold">{selectedIntegration} Integration</h3>
                <p className="text-sm text-muted-foreground mt-1">Coming Soon!</p>
              </div>

              <p className="text-sm text-muted-foreground">
                We're working hard to bring you seamless {selectedIntegration} integration.
                Stay tuned for updates!
              </p>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowComingSoon(false)}
                  className="flex-1"
                >
                  Got it
                </Button>
                <Button
                  onClick={() => {
                    setShowComingSoon(false);
                    setShowUploadSection(true);
                  }}
                  className="flex-1"
                >
                  Upload Files Instead
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </motion.div>
  );
};
