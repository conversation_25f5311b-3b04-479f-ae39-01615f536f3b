'use client'

import React, { useState, useCallback, useEffect, Suspense, lazy } from 'react';
import { Upload, FileSpreadsheet, Save, RefreshCw, Table, Bar<PERSON><PERSON>, Loader2 } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from 'sonner';
import { useAuth } from "@clerk/nextjs";
import { TableTab } from '../TableTab';
import { DataOverview } from "../DataOverview";
import { UploadSection } from './UploadSection';
import { VersionHistory } from './VersionHistory';
import { SaveDatasetDialog } from './SaveDatasetDialog';
import { DatasetInfo } from './DatasetInfo';
import { MiniSidebar } from './MiniSidebar';
import { EmptyStateIllustration } from './EmptyStateIllustration';
import { DataTableSkeleton } from './DataTableSkeleton';
import { sanitizeExcelData, validateDataset } from './utils';
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter, useSearchParams } from "next/navigation";



interface DataRow {
  [key: string]: unknown;
}

interface SavedDataset {
  id: string;
  name: string;
  description?: string;
  fileType: string;
  createdAt: string;
  data: any[];
  headers: string[];
  folderId?: string | null;
}

interface StorageInfo {
  used: number;
  total: number;
  percentage: number;
}

interface SaveFormState {
  name: string;
  description: string;
}

interface VersionHistory {
  id: string;
  versionNumber: number;
  changes: {
    type: 'edit' | 'deleteRow' | 'deleteColumn';
    row?: number;
    column?: string;
    oldValue?: any;
    newValue?: any;
  }[];
  createdAt: Date;
  user: {
    name: string;
  };
}

interface DataProcessingOptions {
  removeNulls: boolean;
  standardize: boolean;
  removeOutliers: boolean;
  fillMissingValues: 'mean' | 'median' | 'mode' | 'none';
}

const DataEditorMain: React.FC = () => {
  // State Management
  const [data, setData] = useState<DataRow[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [fileName, setFileName] = useState<string>('');
  const [dataProcessingOptions, setDataProcessingOptions] = useState<DataProcessingOptions>({
    removeNulls: true,
    standardize: false,
    removeOutliers: false,
    fillMissingValues: 'mean'
  });
  const [showUpload, setShowUpload] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showSaveDialog, setShowSaveDialog] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveForm, setSaveForm] = useState<SaveFormState>({
    name: '',
    description: ''
  });
  const [savedDatasets, setSavedDatasets] = useState<SavedDataset[]>([]);
  const [folders, setFolders] = useState<any[]>([]);
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(false);
  const [storageInfo, setStorageInfo] = useState<StorageInfo>({
    used: 0,
    total: 5 * 1024 * 1024, // 5MB in bytes
    percentage: 0
  });
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [currentDatasetId, setCurrentDatasetId] = useState<string | null>(null);
  const [versionHistory, setVersionHistory] = useState<VersionHistory[]>([]);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [isLoadingVersion, setIsLoadingVersion] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("visualize");
  const [allVersions, setAllVersions] = useState<Record<string, VersionHistory[]>>({});
  // Add state for loading dataset
  const [isLoadingDataset, setIsLoadingDataset] = useState(false);
  // Add state for loading version history
  const [isLoadingVersionHistory, setIsLoadingVersionHistory] = useState(false);

  // Add auth hook
  const { isLoaded, isSignedIn } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Data Processing Functions
  const processData = useCallback((rawData: DataRow[]) => {
    let processedData = [...rawData];

    if (dataProcessingOptions.removeNulls) {
      processedData = processedData.filter(row =>
        Object.values(row as Record<string, unknown>).every(value => value != null)
      );
    }

    if (dataProcessingOptions.standardize) {
      headers.forEach(header => {
        const values = processedData.map(row => Number(row[header])).filter(v => !isNaN(v));
        if (values.length) {
          const mean = values.reduce((a, b) => a + b, 0) / values.length;
          const std = Math.sqrt(values.reduce((a, b) => a + (b - mean) ** 2, 0) / values.length);
          processedData = processedData.map(row => ({
            ...row,
            [header]: !isNaN(Number(row[header])) ? (Number(row[header]) - mean) / std : row[header]
          }));
        }
      });
    }

    return processedData;
  }, [headers, dataProcessingOptions]);

  // This one is for the toolbar button - just shows the dialog
  const handleSaveClick = async () => {
    if (!isLoaded || !isSignedIn) {
      toast.error('Please sign in to save datasets');
      return;
    }

    setShowSaveDialog(true);
  };

  // This one is for the actual saving process in the dialog
  const handleSaveToDb = async () => {
    try {
      setIsSaving(true);
      setProcessingStage('Validating data...');

      // Sanitize the data first
      const sanitizedData = sanitizeExcelData(data);

      // Validate the sanitized data
      const validation = validateDataset(sanitizedData);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      setProcessingStage('Preparing data for save...');

      const savePromise = fetch('/api/datasets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: saveForm.name,
          description: saveForm.description,
          data: sanitizedData,
          headers: headers,
          fileType: fileName.endsWith('.csv') ? 'csv' : 'excel'
        })
      });

      // Use toast.promise for better UX
      await toast.promise(savePromise, {
        loading: 'Saving dataset...',
        success: async (response) => {
          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to save dataset');
          }
          setShowSaveDialog(false);
          setSaveForm({ name: '', description: '' });
          await fetchSavedDatasets();
          return 'Dataset saved successfully';
        },
        error: (error) => {
          console.error('Save error:', error);
          return `Failed to save dataset: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      });

    } catch (error) {
      console.error('Save error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save dataset');
    } finally {
      setIsSaving(false);
      setProcessingStage('');
    }
  };

  // Add function to fetch saved datasets and folders
  const fetchSavedDatasets = async () => {
    if (!isLoaded || !isSignedIn) {
      return;
    }

    setIsLoadingDatasets(true);
    try {
      // Fetch datasets
      const response = await fetch('/api/datasets', {
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to fetch datasets');
      const { datasets } = await response.json();

      console.log('Datasets fetched:', datasets);

      // Calculate total storage used
      const totalUsed = datasets.reduce((acc: number, dataset: any) => {
        return acc + new Blob([JSON.stringify(dataset.data)]).size;
      }, 0);

      setStorageInfo({
        used: totalUsed,
        total: 5 * 1024 * 1024, // 5MB
        percentage: (totalUsed / (5 * 1024 * 1024)) * 100
      });

      // Make sure each dataset has a folderId property (even if null)
      const datasetsWithFolderInfo = datasets.map((dataset: any) => ({
        ...dataset,
        folderId: dataset.folderId || null
      }));

      setSavedDatasets(datasetsWithFolderInfo);

      // Fetch folders
      try {
        const foldersResponse = await fetch('/api/datasets/folders');
        if (foldersResponse.ok) {
          const foldersData = await foldersResponse.json();
          if (foldersData.success) {
            console.log('Folders fetched:', foldersData.folders);
            console.log('Root datasets fetched:', foldersData.datasets);
            setFolders(foldersData.folders || []);

            // If we have root datasets from the folders API, we can use those too
            if (foldersData.datasets && foldersData.datasets.length > 0) {
              // Merge with existing datasets to ensure we have everything
              const allDatasets = [...datasetsWithFolderInfo];

              // Add any root datasets that might not be in the main dataset list
              foldersData.datasets.forEach((rootDataset: any) => {
                if (!allDatasets.some(d => d.id === rootDataset.id)) {
                  allDatasets.push(rootDataset);
                }
              });

              setSavedDatasets(allDatasets);
            }
          }
        }
      } catch (folderError) {
        console.error('Error fetching folders:', folderError);
        // Don't show error toast for folders as it's not critical
      }

      // Fetch version histories for all datasets
      await fetchAllVersionHistories(datasets);
    } catch (error) {
      console.error('Error fetching datasets:', error);
      toast.error('Failed to load saved datasets');
    } finally {
      setIsLoadingDatasets(false);
    }
  };

  // Add function to fetch all version histories
  const fetchAllVersionHistories = async (datasets: SavedDataset[]) => {
    if (datasets.length === 0) return;

    console.log(`Fetching version histories for ${datasets.length} datasets`);

    try {
      // Create a map to store versions by dataset ID
      const versionsByDataset: Record<string, VersionHistory[]> = {};

      // Fetch versions for each dataset sequentially to avoid race conditions
      for (const dataset of datasets) {
        try {
          const response = await fetch(`/api/datasets?datasetId=${dataset.id}`);
          const result = await response.json();

          if (result.success && result.versions) {
            versionsByDataset[dataset.id] = result.versions.sort(
              (a: VersionHistory, b: VersionHistory) => b.versionNumber - a.versionNumber
            );
            console.log(`Found ${result.versions.length} versions for dataset ${dataset.id}`);
          } else {
            console.log(`No versions found for dataset ${dataset.id}`);
            versionsByDataset[dataset.id] = [];
          }
        } catch (err) {
          console.error(`Error fetching versions for dataset ${dataset.id}:`, err);
          versionsByDataset[dataset.id] = [];
        }
      }

      // Store the complete version map in state for reference
      setAllVersions(versionsByDataset);

      // Create a flattened array of all versions (for backward compatibility)
      const allVersionsFlat = Object.values(versionsByDataset).flat();
      setVersionHistory(allVersionsFlat);

      console.log('All versions fetched:', versionsByDataset);
    } catch (error) {
      console.error('Error fetching all version histories:', error);
    }
  };

  // Add useEffect to fetch datasets on mount
  useEffect(() => {
    console.log('DataEditorMain mounted, fetching datasets...');
    fetchSavedDatasets();
  }, []);

  // Add useEffect to log when savedDatasets changes
  useEffect(() => {
    console.log('DataEditorMain: savedDatasets updated:', savedDatasets.length, 'datasets');
    if (savedDatasets.length > 0) {
      console.log('DataEditorMain: First dataset:', {
        id: savedDatasets[0].id,
        name: savedDatasets[0].name,
        folderId: savedDatasets[0].folderId || 'none'
      });
    }

    // If we have datasets but no folders, fetch folders again
    if (savedDatasets.length > 0 && folders.length === 0) {
      console.log('DataEditorMain: We have datasets but no folders, fetching folders again...');
      try {
        fetch('/api/datasets/folders')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              console.log('DataEditorMain: Folders fetched:', data.folders?.length || 0);
              setFolders(data.folders || []);
            }
          })
          .catch(error => {
            console.error('DataEditorMain: Error fetching folders:', error);
          });
      } catch (error) {
        console.error('DataEditorMain: Error fetching folders:', error);
      }
    }
  }, [savedDatasets, folders]);

  // On mount, if datasetId is present in the URL, auto-load that dataset
  useEffect(() => {
    const datasetId = searchParams.get("datasetId");
    if (datasetId) {
      fetchAndLoadDataset(datasetId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update the handleLoadDataset function
  const handleLoadDataset = (dataset: SavedDataset) => {
    try {
      setData(dataset.data);
      setHeaders(dataset.headers);
      setFileName(dataset.name);
      setCurrentDatasetId(dataset.id);
      setShowUpload(false);

      toast.success('Dataset loaded successfully');
    } catch (error) {
      console.error('Error loading dataset:', error);
      toast.error('Failed to load dataset');
    }
  };

  // Add delete function
  const handleDeleteDataset = async (datasetId: string) => {
    try {
      const response = await fetch(`/api/datasets/${datasetId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete dataset');
      }

      toast.success('Dataset deleted successfully');
      await fetchSavedDatasets();
    } catch (error) {
      console.error('Error deleting dataset:', error);
      toast.error('Failed to delete dataset');
    }
  };

  // Enhanced function to fetch version history with loading state
  const fetchVersionHistory = async (datasetId: string, showModal: boolean = false) => {
    if (showModal) {
      setIsLoadingVersionHistory(true);
      setShowVersionHistory(true);

      // Show loading toast
      toast.loading('Loading version history...', {
        id: `version-history-${datasetId}`
      });
    }

    try {
      console.log(`Fetching version history for dataset ID: ${datasetId}`);
      const response = await fetch(`/api/datasets?datasetId=${datasetId}`);
      const result = await response.json();

      if (result.success) {
        // Log the actual versions returned from the API
        console.log(`Versions retrieved for ${datasetId}:`, result.versions);

        // Set only the versions for this specific dataset
        setVersionHistory(result.versions || []);

        // Log whether any versions were found
        if (!result.versions || result.versions.length === 0) {
          console.log(`No versions found for dataset ${datasetId}. API returned empty array.`);
        }

        if (showModal) {
          toast.success(`Found ${result.versions?.length || 0} versions`, {
            id: `version-history-${datasetId}`
          });
        }
      } else {
        console.error(`Failed to fetch versions for dataset ${datasetId}:`, result.error);
        if (showModal) {
          toast.error('Failed to load version history', {
            id: `version-history-${datasetId}`
          });
          setShowVersionHistory(false);
        }
      }
    } catch (error) {
      console.error('Error fetching version history:', error);
      if (showModal) {
        toast.error('Failed to load version history', {
          id: `version-history-${datasetId}`
        });
        setShowVersionHistory(false);
      }
    } finally {
      if (showModal) {
        setIsLoadingVersionHistory(false);
      }
    }
  };

  // Update the loadVersionData function
  const loadVersionData = async (datasetId: string, versionNumber: number) => {
    if (isLoadingVersion) return; // Prevent multiple clicks
    setIsLoadingVersion(true);
    try {
      toast.loading(`Loading version ${versionNumber}...`, {
        id: `load-version-${datasetId}-${versionNumber}`
      });
      const response = await fetch(`/api/datasets/${datasetId}/versions/${versionNumber}`);
      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to load version');
      }
      setData(Array.isArray(data.data) ? data.data : []);
      setHeaders(Array.isArray(data.headers) ? data.headers : (Array.isArray(data.data) && data.data.length > 0 ? Object.keys(data.data[0]) : []));
      setCurrentDatasetId(datasetId);
      setShowVersionHistory(false);
      setActiveTab('table');
      toast.success(`Version ${versionNumber} loaded successfully`, {
        id: `load-version-${datasetId}-${versionNumber}`
      });
    } catch (error) {
      console.error('Error loading version:', error);
      toast.error('Failed to load version', {
        id: `load-version-${datasetId}-${versionNumber}`
      });
    } finally {
      setIsLoadingVersion(false);
    }
  };

  // Add this to handle dataset selection
  const handleDatasetSelect = (dataset: SavedDataset) => {
    try {
      setCurrentDatasetId(dataset.id);
      setData(Array.isArray(dataset.data) ? dataset.data : []);
      setHeaders(Array.isArray(dataset.headers) ? dataset.headers : Array.isArray(dataset.data) && dataset.data.length > 0 ? Object.keys(dataset.data[0]) : []);
      setFileName(dataset.name);
      setShowUpload(false);
      setActiveTab('table'); // Switch to table view

      // Fetch version history (background loading, no modal)
      fetchVersionHistory(dataset.id, false);

      toast.success('Dataset loaded successfully');
    } catch (error) {
      console.error('Error loading dataset:', error);
      toast.error('Failed to load dataset');
    }
  };

  // New function to handle showing version history modal
  const handleShowVersionHistory = (dataset: SavedDataset) => {
    // Set current dataset but don't switch tabs
    setCurrentDatasetId(dataset.id);
    setFileName(dataset.name);

    // Fetch version history and show modal with loading state
    fetchVersionHistory(dataset.id, true);
  };

  // Add a new async function to fetch and load a dataset by ID
  const fetchAndLoadDataset = async (datasetId: string) => {
    setActiveTab('table');
    setIsLoadingDataset(true);
    try {
      const response = await fetch(`/api/datasets?datasetId=${datasetId}`);
      const result = await response.json();
      if (result.success && result.datasetInfo) {
        setData(Array.isArray(result.datasetInfo.data) ? result.datasetInfo.data : []);
        setHeaders(Array.isArray(result.datasetInfo.headers) ? result.datasetInfo.headers : (Array.isArray(result.datasetInfo.data) && result.datasetInfo.data.length > 0 ? Object.keys(result.datasetInfo.data[0]) : []));
        setFileName(result.datasetInfo.name || '');
        setCurrentDatasetId(result.datasetInfo.id);
        setShowUpload(false);
        setActiveTab('table');
        toast.success('Dataset loaded successfully');
        router.push(`?datasetId=${result.datasetInfo.id}`);
      } else {
        toast.error('Failed to load dataset');
      }
    } catch (error) {
      console.error('Error loading dataset:', error);
      toast.error('Failed to load dataset');
    } finally {
      setIsLoadingDataset(false);
    }
  };

  // Helper function for processing stage
  const [processingStage, setProcessingStage] = useState<string>('');

  // Handler for data loaded from upload
  const handleDataLoaded = (newData: any[], newHeaders: string[], newFileName: string) => {
    setData(newData);
    setHeaders(newHeaders);
    setFileName(newFileName);
    setShowUpload(false);
  };

  // Render dataset info for the upload view
  const renderDatasetInfo = () => {
    return (
      <DatasetInfo
        savedDatasets={savedDatasets}
        allVersions={allVersions}
        onDatasetSelect={handleDatasetSelect}
        onDeleteDataset={handleDeleteDataset}
        onShowVersionHistory={handleShowVersionHistory}
        loadVersionData={loadVersionData}
        onDataLoaded={handleDataLoaded}
        storageInfo={storageInfo}
        isLoadingDatasets={isLoadingDatasets}
        renderDatasetInfo={renderDatasetInfo}
      />
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/40 to-background/80 overflow-hidden relative">
      <div className="flex h-screen">
        {/* Mini Sidebar - Always visible */}
        <aside className="h-full border-r bg-card/80 shadow-md flex-shrink-0 transition-all duration-300" style={{ minWidth: isSidebarCollapsed ? '60px' : '220px' }}>
        <MiniSidebar
          savedDatasets={savedDatasets}
          storageInfo={storageInfo}
          isLoadingDatasets={isLoadingDatasets}
          onDatasetSelect={handleDatasetSelect}
          onDeleteDataset={handleDeleteDataset}
          onShowVersionHistory={handleShowVersionHistory}
          onUploadClick={() => {
            setShowUpload(true);
            setData([]);
            setHeaders([]);
            setError(null);
          }}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
        />
        </aside>

        {/* Main Content Area */}
        <main className="flex-1 flex flex-col justify-start py-8 overflow-y-auto bg-background/80 w-full">
          <div className="w-full">
        {showUpload ? (
              <div className="w-full">
            <UploadSection
              onDataLoaded={handleDataLoaded}
              savedDatasets={savedDatasets}
              storageInfo={storageInfo}
              isLoadingDatasets={isLoadingDatasets}
              onDatasetSelect={handleDatasetSelect}
              onDeleteDataset={handleDeleteDataset}
              onShowVersionHistory={handleShowVersionHistory}
              allVersions={allVersions}
              renderDatasetInfo={renderDatasetInfo}
            />
          </div>
        ) : (
              <div className="flex flex-col gap-0 w-full h-full">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col w-full h-full">
                  <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="flex items-center gap-4 px-2 pt-2">
                      <TabsList className="h-9">
                        <TabsTrigger value="visualize" className="flex items-center text-xs">
                          <BarChart className="h-4 w-4 mr-1" />
                          Visualize
                        </TabsTrigger>
                        <TabsTrigger value="table" className="flex items-center text-xs">
                          <Table className="h-4 w-4 mr-1" />
                          Table
                        </TabsTrigger>
                      </TabsList>
                      <div className="flex items-center gap-2 ml-auto">
                        <Badge variant="secondary" className="h-8 flex items-center gap-1">
                          <FileSpreadsheet className="h-4 w-4" />
                          <span className="text-xs">{fileName}</span>
                        </Badge>
                        <div className="flex items-center gap-1">
                          <Button variant="outline" size="sm" className="h-8" onClick={() => setData(processData(data))}>
                            <RefreshCw className="h-4 w-4 mr-1.5" />
                            <span className="text-xs">Refresh</span>
                          </Button>
                          <Button variant="outline" size="sm" className="h-8" onClick={() => { setShowUpload(true); setData([]); setHeaders([]); setError(null); }}>
                            <Upload className="h-4 w-4 mr-1.5" />
                            <span className="text-xs">Upload</span>
                          </Button>
                          <Button variant="default" size="sm" className="h-8" onClick={handleSaveClick}>
                            <Save className="h-4 w-4 mr-1.5" />
                            <span className="text-xs">Save</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <TabsContent value="visualize" className="w-full h-full">
                    <Suspense fallback={<div className="flex items-center justify-center h-24"><Loader2 className="h-6 w-6 animate-spin text-primary/80" /></div>}>
                      <DataOverview
                        data={data}
                        headers={headers}
                        title="Data Summary"
                        description="Overview of numeric columns"
                        isLoadingDataset={isLoadingDataset || isLoadingVersion}
                      />
                    </Suspense>
                    <Suspense fallback={<div className="flex flex-col gap-4 p-8"><Skeleton className="h-8 w-1/3 mb-2" /><Skeleton className="h-40 w-full rounded-xl" /><Skeleton className="h-8 w-1/4 mt-4" /></div>}>
                      <DatasetInfo
                        savedDatasets={savedDatasets}
                        allVersions={allVersions}
                        onDatasetSelect={(dataset) => fetchAndLoadDataset(dataset.id)}
                        onDeleteDataset={handleDeleteDataset}
                        onShowVersionHistory={(dataset) => handleShowVersionHistory(dataset)}
                        loadVersionData={loadVersionData}
                        onDataLoaded={handleDataLoaded}
                        storageInfo={storageInfo}
                        isLoadingDatasets={isLoadingDatasets}
                        renderDatasetInfo={renderDatasetInfo}
                      />
                    </Suspense>
                  </TabsContent>
                  <TabsContent value="table" className="w-full h-full">
                    {isLoadingDataset ? (
                      <DataTableSkeleton rows={8} columns={5} />
                    ) : (
                      <Suspense fallback={<div className="flex items-center justify-center h-full"><Loader2 className="h-10 w-10 animate-spin text-primary/80" /></div>}>
                        <TableTab
                          // @ts-ignore
                          data={data}
                          headers={headers}
                          datasetId={currentDatasetId || undefined}
                          totalRows={data.length}
                          currentPage={1}
                          isLoadingMore={false}
                          isLoadingDataset={isLoadingDataset || isLoadingVersion}
                        />
                      </Suspense>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Save Dialog */}
      <SaveDatasetDialog
        showSaveDialog={showSaveDialog}
        setShowSaveDialog={setShowSaveDialog}
        saveForm={saveForm}
        setSaveForm={setSaveForm}
        handleSaveToDb={handleSaveToDb}
        isSaving={isSaving}
      />

      {/* Version History Dialog */}
      <Suspense fallback={<div className="flex flex-col gap-4 p-8"><Skeleton className="h-8 w-1/3 mb-2" /><Skeleton className="h-40 w-full rounded-xl" /><Skeleton className="h-8 w-1/4 mt-4" /></div>}>
        <VersionHistory
          showVersionHistory={showVersionHistory}
          setShowVersionHistory={setShowVersionHistory}
          versionHistory={versionHistory}
          currentDatasetId={currentDatasetId}
          loadVersionData={loadVersionData}
          isLoadingVersion={isLoadingVersionHistory || isLoadingVersion}
          datasetName={fileName}
        />
      </Suspense>
    </div>
  );
};

export default DataEditorMain;
