# Debug Tab Switching Issue

## Problem
Users cannot switch between tabs (Table, Chart, Output) in the ChartBuilder cells.

## Debugging Steps

### 1. Check Console Logs
When you click on the tabs, you should see these console messages:
- `🔄 Table button clicked, current viewMode: [current_mode]`
- `🔄 Output button clicked, current viewMode: [current_mode]`
- `🔄 Manual view mode change: [old_mode] → [new_mode]`
- `📥 Prop viewMode changed: [old_mode] → [new_mode]`

### 2. Test Cases

#### Test Case 1: DataFrame Display
```python
import pandas as pd

df = pd.DataFrame({
    'Name': ['Alice', '<PERSON>', '<PERSON>'],
    'Age': [25, 30, 35],
    'City': ['New York', 'London', 'Tokyo']
})

result = df
```
**Expected**: Should auto-switch to Table tab, then allow manual switching to Output tab.

#### Test Case 2: Text Output
```python
print("Hello World!")
print("This is text output")
```
**Expected**: Should auto-switch to Output tab, then allow manual switching to Table tab (if data exists).

#### Test Case 3: Interactive Input
```python
name = input("What's your name? ")
print(f"Hello {name}!")
```
**Expected**: Should auto-switch to Output tab for input, then allow manual switching.

### 3. Common Issues and Solutions

#### Issue 1: Auto-switching Overrides Manual Selection
**Symptoms**: Tabs switch back automatically after clicking
**Solution**: The `hasAutoSwitched` flag should prevent this

#### Issue 2: Buttons Not Responding
**Symptoms**: No console logs when clicking buttons
**Solution**: Check for CSS issues or event propagation problems

#### Issue 3: ViewMode Not Syncing
**Symptoms**: Button appears active but content doesn't change
**Solution**: Check prop passing between Cell and QueryResult components

### 4. Quick Fixes to Try

1. **Clear Browser Cache**: Sometimes cached JavaScript can cause issues
2. **Check Network Tab**: Ensure API calls are completing successfully
3. **Inspect Element**: Check if buttons have proper event listeners
4. **Console Errors**: Look for any JavaScript errors that might be breaking the functionality

### 5. Manual Testing Steps

1. Run a Python cell that creates a DataFrame
2. Verify it auto-switches to Table tab
3. Click on Output tab - should switch and stay there
4. Click on Table tab - should switch back
5. Run a cell with print statements
6. Verify it auto-switches to Output tab
7. Try switching tabs manually

If any of these steps fail, check the console logs for the debugging messages we added.
