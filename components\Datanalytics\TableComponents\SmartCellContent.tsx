'use client'

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SmartCellContentProps {
  value: any;
  columnWidth: number;
  isEmpty: boolean;
  onDoubleClick: () => void;
}

export const SmartCellContent: React.FC<SmartCellContentProps> = ({
  value,
  columnWidth,
  isEmpty,
  onDoubleClick
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showOverflow, setShowOverflow] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const textContent = String(value || '');
  const isLongText = textContent.length > 30;

  useEffect(() => {
    if (contentRef.current) {
      const element = contentRef.current;
      const isOverflowing = element.scrollWidth > element.clientWidth;
      setShowOverflow(isOverflowing);
    }
  }, [textContent, columnWidth]);

  const handleMouseEnter = () => {
    if (isLongText) {
      setIsExpanded(true);
    }
  };

  const handleMouseLeave = () => {
    setIsExpanded(false);
  };

  if (isEmpty) {
    return (
      <div 
        className="w-full h-full flex items-center px-1 py-1 text-muted-foreground/50 italic text-sm cursor-cell"
        onDoubleClick={onDoubleClick}
      >
        <span className="text-xs">Empty</span>
      </div>
    );
  }

  return (
    <div 
      className="relative w-full h-full"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onDoubleClick={onDoubleClick}
    >
      {/* Normal cell content */}
      <div 
        ref={contentRef}
        className={cn(
          "w-full h-full flex items-center px-1 py-1 text-sm cursor-cell",
          "transition-all duration-200",
          isExpanded ? "opacity-50" : "opacity-100"
        )}
        style={{ minWidth: `${columnWidth}px` }}
      >
        <span className={cn(
          "leading-tight",
          isLongText ? "truncate" : "break-words"
        )}>
          {textContent}
        </span>
        
        {/* Overflow indicator */}
        {showOverflow && !isExpanded && (
          <div className="absolute bottom-0.5 right-0.5">
            <div className="w-1.5 h-1.5 bg-amber-500/70 rounded-full animate-pulse"></div>
          </div>
        )}
      </div>

      {/* Expanded overlay for long text */}
      <AnimatePresence>
        {isExpanded && isLongText && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 5 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 5 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
            className="absolute top-0 left-0 z-50 bg-background border border-primary/30 rounded-md shadow-lg p-2 min-w-max max-w-sm"
            style={{
              minWidth: `${Math.max(columnWidth, 200)}px`,
              maxWidth: '400px'
            }}
          >
            <div className="text-sm leading-relaxed break-words">
              {textContent}
            </div>
            
            {/* Gradient fade at bottom if text is very long */}
            {textContent.length > 200 && (
              <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-background to-transparent pointer-events-none rounded-b-md"></div>
            )}
            
            {/* Small arrow pointing to original cell */}
            <div className="absolute -bottom-1 left-4 w-2 h-2 bg-background border-r border-b border-primary/30 transform rotate-45"></div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Enhanced cell wrapper with smart overflow handling
interface EnhancedCellWrapperProps {
  value: any;
  columnWidth: number;
  onDoubleClick: () => void;
  className?: string;
}

export const EnhancedCellWrapper: React.FC<EnhancedCellWrapperProps> = ({
  value,
  columnWidth,
  onDoubleClick,
  className
}) => {
  const isEmpty = value === null || value === undefined || value === '';
  const textContent = String(value || '');
  const isVeryLong = textContent.length > 100;
  
  return (
    <div className={cn(
      "relative group transition-all duration-150",
      "hover:bg-primary/5 hover:shadow-sm hover:z-10",
      "focus-within:bg-primary/10 focus-within:ring-1 focus-within:ring-primary/30 focus-within:z-20",
      className
    )}>
      <SmartCellContent
        value={value}
        columnWidth={columnWidth}
        isEmpty={isEmpty}
        onDoubleClick={onDoubleClick}
      />
      
      {/* Edit indicator */}
      <div className="absolute top-0.5 right-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="w-2 h-2 bg-primary/60 rounded-sm"></div>
      </div>
      
      {/* Long text indicator */}
      {isVeryLong && (
        <div className="absolute top-0.5 left-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="w-1 h-1 bg-blue-500/70 rounded-full"></div>
        </div>
      )}
    </div>
  );
};

// Utility function to estimate text width
export const estimateTextWidth = (text: string, fontSize: number = 14): number => {
  // Rough estimation: average character width is about 0.6 * fontSize
  const avgCharWidth = fontSize * 0.6;
  return Math.ceil(text.length * avgCharWidth);
};

// Utility function to calculate optimal column width
export const calculateOptimalWidth = (
  values: any[], 
  headerText: string, 
  minWidth: number = 80, 
  maxWidth: number = 300
): number => {
  const allTexts = [
    headerText,
    ...values.map(v => String(v || '')).slice(0, 50) // Sample first 50 for performance
  ];
  
  const maxTextWidth = Math.max(
    ...allTexts.map(text => estimateTextWidth(text))
  );
  
  // Add padding (16px) and some buffer (20px)
  const calculatedWidth = maxTextWidth + 36;
  
  return Math.max(minWidth, Math.min(maxWidth, calculatedWidth));
};
