'use client'

import React from 'react';
import { motion } from "framer-motion";
import { Database, Eye, GitBranch, Trash2, Clock, User, Activity, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from '@/components/ui/badge';
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { IntegrationCard } from './IntegrationCard';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { formatBytes, formatNumber, formatDate, calculateDataSize } from './utils';

interface DatasetInfoProps {
  savedDatasets: any[];
  allVersions: Record<string, any[]>;
  onDatasetSelect: (dataset: any) => void;
  onDeleteDataset: (datasetId: string) => void;
  onShowVersionHistory: (dataset: any) => void;
  loadVersionData: (datasetId: string, versionNumber: number) => void;
  onDataLoaded?: (data: any[], headers: string[], fileName: string) => void;
  storageInfo?: {
    used: number;
    total: number;
    percentage: number;
  };
  isLoadingDatasets?: boolean;
  renderDatasetInfo?: () => React.ReactNode;
}

export const DatasetInfo: React.FC<DatasetInfoProps> = ({
  savedDatasets,
  allVersions,
  onDatasetSelect,
  onDeleteDataset,
  onShowVersionHistory,
  loadVersionData,
  onDataLoaded,
  storageInfo = { used: 0, total: 100, percentage: 0 },
  isLoadingDatasets = false,
  renderDatasetInfo = () => null
}) => {
  if (!savedDatasets.length) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full mt-6 space-y-6"
    >
      {/* Integration Card */}
      <IntegrationCard
        onDataLoaded={onDataLoaded}
        savedDatasets={savedDatasets}
        storageInfo={storageInfo}
        isLoadingDatasets={isLoadingDatasets}
        onDatasetSelect={onDatasetSelect}
        onDeleteDataset={onDeleteDataset}
        onShowVersionHistory={onShowVersionHistory}
        allVersions={allVersions}
        renderDatasetInfo={renderDatasetInfo}
      />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold">Your Datasets</h2>
          <p className="text-muted-foreground text-sm">Manage and explore your data collections</p>
        </div>
        <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
          <Database className="h-3 w-3 mr-1" />
          {savedDatasets.length} dataset{savedDatasets.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      {/* Enhanced Table */}
      <Card className="rounded-xl overflow-hidden border-0 shadow-lg bg-gradient-to-br from-background to-muted/20">
        <CardHeader className="border-b bg-muted/30 py-3">
          <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-muted-foreground">
            <div className="col-span-4">Dataset</div>
            <div className="col-span-2">Statistics</div>
            <div className="col-span-4">Version History</div>
            <div className="col-span-2 text-right">Actions</div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="space-y-0">
            {savedDatasets.map((dataset, index) => {
              const datasetVersions = allVersions[dataset.id] || [];
              return (
                <motion.div
                  key={dataset.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="grid grid-cols-12 gap-4 p-4 border-b border-border/50 hover:bg-muted/20 transition-all duration-200 group"
                >
                  {/* Dataset Info */}
                  <div className="col-span-4 flex items-center gap-3">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary/20 to-primary/5 flex items-center justify-center border border-primary/20"
                    >
                      <Database className="h-5 w-5 text-primary" />
                    </motion.div>
                    <div className="min-w-0 flex-1">
                      <p className="font-semibold truncate text-base leading-tight group-hover:text-primary transition-colors">
                        {dataset.name}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <p className="text-xs text-muted-foreground truncate">
                          {formatDate(dataset.createdAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                  {/* Statistics */}
                  <div className="col-span-2 space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="p-1 bg-blue-500/10 rounded">
                        <TrendingUp className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-sm font-medium">
                        {formatNumber(dataset.data?.length ?? 0)} rows
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="p-1 bg-emerald-500/10 rounded">
                        <Database className="h-3 w-3 text-emerald-600" />
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {dataset.headers?.length ?? 0} columns
                      </span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatBytes(calculateDataSize(dataset.data ?? []))}
                    </div>
                  </div>
                  {/* Enhanced Version Timeline */}
                  <div className="col-span-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <GitBranch className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs font-medium">Version History</span>
                        {datasetVersions.length > 0 && (
                          <Badge variant="outline" className="text-xs h-4 px-1">
                            {datasetVersions.length}
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-1 flex-wrap">
                        {datasetVersions.length > 0 ? (
                          datasetVersions.slice(0, 6).map((version, vIndex) => (
                            <motion.button
                              key={version.id}
                              onClick={() => loadVersionData(dataset.id, version.versionNumber)}
                              whileHover={{ scale: 1.15, y: -2 }}
                              whileTap={{ scale: 0.95 }}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: vIndex * 0.05 }}
                              className={cn(
                                "relative h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold border-2 transition-all duration-200",
                                version.changes.length > 0
                                  ? "bg-primary text-primary-foreground border-primary shadow-lg shadow-primary/25"
                                  : "bg-muted text-muted-foreground border-muted-foreground/20 hover:border-primary/50"
                              )}
                            >
                              {version.versionNumber}
                              {version.changes.length > 0 && (
                                <motion.span
                                  animate={{ scale: [1, 1.2, 1] }}
                                  transition={{ duration: 2, repeat: Infinity }}
                                  className="absolute -top-0.5 -right-0.5 h-2 w-2 rounded-full bg-green-500 border border-background"
                                />
                              )}
                            </motion.button>
                          ))
                        ) : (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground italic">
                            <Activity className="h-3 w-3" />
                            <span>No versions yet</span>
                          </div>
                        )}
                        {datasetVersions.length > 6 && (
                          <Badge variant="secondary" className="text-xs h-6 px-2">
                            +{datasetVersions.length - 6}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  {/* Enhanced Actions */}
                  <div className="col-span-2 flex items-center gap-1 justify-end">
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 hover:bg-primary/10 hover:text-primary"
                        onClick={() => onDatasetSelect(dataset)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 hover:bg-blue-500/10 hover:text-blue-600"
                        onClick={() => {
                          onDatasetSelect(dataset);
                          onShowVersionHistory(dataset);
                        }}
                      >
                        <GitBranch className="h-4 w-4" />
                      </Button>
                    </motion.div>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </motion.div>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="rounded-xl">
                        <AlertDialogHeader>
                          <AlertDialogTitle className="flex items-center gap-2">
                            <div className="p-2 bg-destructive/10 rounded-lg">
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </div>
                            Delete Dataset
                          </AlertDialogTitle>
                          <AlertDialogDescription className="text-base">
                            Are you sure you want to delete <strong>"{dataset.name}"</strong>?
                            This action cannot be undone and will permanently remove all data and version history.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="rounded-lg">Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => onDeleteDataset(dataset.id)}
                            className="bg-destructive hover:bg-destructive/90 rounded-lg"
                          >
                            Delete Forever
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

