#!/usr/bin/env python3
"""
Test clean Jupyter-style interactive input
"""

print("🎯 Clean Jupyter-Style Interactive Test")
print("=" * 40)

# Test 1: Simple input
name = input("What's your name? ")
print(f"Hello, {name}!")

# Test 2: Age input
age = input("How old are you? ")
print(f"You are {age} years old!")

# Test 3: Show some data
data = {"name": name, "age": age}
print("Your data:", data)

# Test 4: Expression result
result = len(name) + int(age) if age.isdigit() else len(name)
print(f"Calculated result: {result}")

# This should display the final result
result
