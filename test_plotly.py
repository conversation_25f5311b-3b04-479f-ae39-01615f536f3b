#!/usr/bin/env python3
"""
Test script to verify Plotly functionality in the HRatlas backend
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from jupyter_kernel import get_kernel

def test_plotly_basic():
    """Test basic Plotly functionality"""
    print("Testing basic Plotly functionality...")
    
    kernel = get_kernel()
    
    # Test basic Plotly plot
    code = """
import plotly.graph_objects as go
import numpy as np

# Create sample data
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Create plot
fig = go.Figure()
fig.add_trace(go.Scatter(x=x, y=y, mode='lines', name='sin(x)'))
fig.update_layout(title='Sine Wave', xaxis_title='X', yaxis_title='Y')

# Show the plot (this should be captured)
fig.show()

print("Plotly plot created!")
"""
    
    result = kernel.execute_code(code)
    
    print(f"Execution status: {result['status']}")
    print(f"Output: {result['stdout']}")
    print(f"Plots captured: {len(result['plots'])}")
    print(f"Plot count: {result.get('plot_count', 0)}")
    print(f"Plotly count: {result.get('plotly_count', 0)}")
    
    if result['plots']:
        for i, plot in enumerate(result['plots']):
            print(f"Plot {i+1}: {len(plot)} characters")
            if plot.startswith('{'):
                print(f"  - Appears to be JSON data")
            elif plot.startswith('data:'):
                print(f"  - Appears to be base64 image")
            else:
                print(f"  - Unknown format: {plot[:100]}...")
    
    return result

def test_matplotlib_basic():
    """Test basic matplotlib functionality"""
    print("\nTesting basic matplotlib functionality...")
    
    kernel = get_kernel()
    
    # Test basic matplotlib plot
    code = """
import matplotlib.pyplot as plt
import numpy as np

# Create sample data
x = np.linspace(0, 10, 100)
y = np.cos(x)

# Create plot
plt.figure(figsize=(10, 6))
plt.plot(x, y, label='cos(x)')
plt.title('Cosine Wave')
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.grid(True)

# Show the plot (this should be captured)
plt.show()

print("Matplotlib plot created!")
"""
    
    result = kernel.execute_code(code)
    
    print(f"Execution status: {result['status']}")
    print(f"Output: {result['stdout']}")
    print(f"Plots captured: {len(result['plots'])}")
    print(f"Plot count: {result.get('plot_count', 0)}")
    
    if result['plots']:
        for i, plot in enumerate(result['plots']):
            print(f"Plot {i+1}: {len(plot)} characters")
            if plot.startswith('data:image'):
                print(f"  - Base64 image data")
            else:
                print(f"  - Other format: {plot[:100]}...")
    
    return result

if __name__ == "__main__":
    print("HRatlas Plotting Test")
    print("=" * 50)
    
    # Test Plotly
    plotly_result = test_plotly_basic()
    
    # Test matplotlib
    matplotlib_result = test_matplotlib_basic()
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Plotly plots captured: {len(plotly_result['plots'])}")
    print(f"Matplotlib plots captured: {len(matplotlib_result['plots'])}")
    
    if len(plotly_result['plots']) > 0:
        print("✅ Plotly plotting is working!")
    else:
        print("❌ Plotly plotting is not working")
        
    if len(matplotlib_result['plots']) > 0:
        print("✅ Matplotlib plotting is working!")
    else:
        print("❌ Matplotlib plotting is not working")
