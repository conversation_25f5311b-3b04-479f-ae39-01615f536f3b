'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { GitBranch, Clock, User, Activity } from 'lucide-react';

export const VersionHistorySkeleton: React.FC = () => {
  return (
    <div className="flex-1 overflow-hidden mt-6">
      <div className="h-[65vh] pr-4 space-y-8">
        {/* Header skeleton */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center gap-3">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
              className="p-2 bg-primary/10 rounded-lg"
            >
              <GitBranch className="h-5 w-5 text-primary/60" />
            </motion.div>
            <div className="space-y-1">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-3 w-48" />
            </div>
          </div>
          <motion.div
          // @ts-ignore
            animate={{ pulse: [1, 1.05, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <Skeleton className="h-6 w-20 rounded-full" />
          </motion.div>
        </motion.div>

        {/* Version cards skeleton */}
        {[...Array(3)].map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.15 }}
            className="relative pl-12 before:absolute before:left-5 before:top-8 before:h-full before:w-[2px] before:bg-gradient-to-b before:from-primary/30 before:to-muted/50 last:before:h-0"
          >
            {/* Animated version number circle */}
            <motion.div
              animate={{ 
                scale: [1, 1.1, 1],
                boxShadow: [
                  "0 0 0 0 rgba(var(--primary), 0.3)",
                  "0 0 0 10px rgba(var(--primary), 0)",
                  "0 0 0 0 rgba(var(--primary), 0)"
                ]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity, 
                delay: index * 0.3 
              }}
              className="absolute left-0 top-2 h-10 w-10 rounded-full border-2 border-primary/30 bg-background flex items-center justify-center shadow-lg"
            >
              <motion.div
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.2 }}
              >
                <Skeleton className="h-4 w-4 rounded" />
              </motion.div>
            </motion.div>

            {/* Version card skeleton */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 + 0.2 }}
            >
              <Card className="overflow-hidden border-l-4 border-l-primary/20 bg-gradient-to-r from-background to-muted/10">
                <CardHeader className="pb-2 pt-4 px-5">
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col space-y-3">
                      {/* Version badge and user info */}
                      <div className="flex items-center gap-3">
                        <motion.div
                          animate={{ opacity: [0.6, 1, 0.6] }}
                          transition={{ duration: 1.5, repeat: Infinity, delay: 0.3 }}
                        >
                          <Skeleton className="h-6 w-12 rounded-full" />
                        </motion.div>
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3 text-muted-foreground/50" />
                          <Skeleton className="h-4 w-16" />
                        </div>
                      </div>
                      
                      {/* Timestamp */}
                      <div className="flex items-center gap-2">
                        <Clock className="h-3.5 w-3.5 text-muted-foreground/50" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {/* Changes badge */}
                      <div className="flex items-center gap-1 bg-muted/30 rounded-md px-2 py-1">
                        <Activity className="h-3 w-3 text-muted-foreground/50" />
                        <Skeleton className="h-3 w-12" />
                      </div>
                      
                      {/* View button */}
                      <motion.div
                        animate={{ opacity: [0.7, 1, 0.7] }}
                        transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                      >
                        <Skeleton className="h-8 w-16 rounded-md" />
                      </motion.div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="px-5 pt-0">
                  <div className="mt-3 space-y-3">
                    {/* Change items skeleton */}
                    {[...Array(2 + Math.floor(Math.random() * 2))].map((_, changeIndex) => (
                      <motion.div
                        key={changeIndex}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 + changeIndex * 0.05 + 0.4 }}
                        className="flex items-start gap-3 p-3 rounded-lg bg-gradient-to-r from-muted/20 to-muted/5 border border-muted/10"
                      >
                        {/* Change type icon */}
                        <motion.div
                          animate={{ 
                            backgroundColor: [
                              "rgba(59, 130, 246, 0.1)", 
                              "rgba(16, 185, 129, 0.1)", 
                              "rgba(239, 68, 68, 0.1)",
                              "rgba(59, 130, 246, 0.1)"
                            ]
                          }}
                          transition={{ 
                            duration: 3, 
                            repeat: Infinity, 
                            delay: changeIndex * 0.5 
                          }}
                          className="p-1 rounded-md"
                        >
                          <Skeleton className="h-3 w-3" />
                        </motion.div>
                        
                        <div className="flex-1 space-y-2">
                          {/* Change description */}
                          <Skeleton className="h-4 w-full max-w-[200px]" />
                          
                          {/* Before/after values (for edit changes) */}
                          {changeIndex % 2 === 0 && (
                            <div className="flex items-center gap-2">
                              <Skeleton className="h-6 w-20 rounded" />
                              <div className="h-3 w-3 rounded-full bg-muted-foreground/20" />
                              <Skeleton className="h-6 w-20 rounded" />
                            </div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                    
                    {/* More changes button skeleton */}
                    {index === 0 && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.8 }}
                        className="text-center pt-3"
                      >
                        <motion.div
                          animate={{ opacity: [0.6, 1, 0.6] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <Skeleton className="h-8 w-32 rounded-md mx-auto" />
                        </motion.div>
                      </motion.div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        ))}

        {/* Loading indicator at bottom */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="flex items-center justify-center py-8"
        >
          <div className="flex items-center gap-3 text-muted-foreground">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              className="h-4 w-4 border-2 border-primary/30 border-t-primary rounded-full"
            />
            <motion.span
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="text-sm"
            >
              Loading version history...
            </motion.span>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
