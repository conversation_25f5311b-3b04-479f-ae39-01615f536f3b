"use client";

import { useMemo } from "react";
import { cn } from "@/lib/utils";
import { Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ColumnBarChartProps {
  data: any[];
  accessor: string;
  width?: number;
  height?: number;
}

interface ColumnStats {
  min: number;
  max: number;
  mean: number;
  median: number;
  stdDev: number;
  values: number[];
  hasData: boolean;
  count: number;
}

export function ColumnBarChart({
  data,
  accessor,
  width = 100, // Increased for better visibility
  height = 28
}: ColumnBarChartProps) {
  // Enhanced statistics calculation with color mapping
  const stats = useMemo<ColumnStats & {
    coloredValues: Array<{ value: number; color: string; intensity: number }>
  }>(() => {
    // Filter out non-numeric values and convert string numbers to numbers
    const numericValues = data
      .map(row => {
        const value = row[accessor];
        if (typeof value === 'number') return value;
        if (typeof value === 'string' && !isNaN(Number(value))) return Number(value);
        return null;
      })
      .filter((val): val is number => val !== null);

    if (numericValues.length === 0) {
      return {
        min: 0,
        max: 0,
        mean: 0,
        median: 0,
        stdDev: 0,
        values: [],
        hasData: false,
        count: 0,
        coloredValues: []
      };
    }

    const min = Math.min(...numericValues);
    const max = Math.max(...numericValues);

    // Calculate mean (average)
    const sum = numericValues.reduce((acc, val) => acc + val, 0);
    const mean = sum / numericValues.length;

    // Calculate median
    const sortedValues = [...numericValues].sort((a, b) => a - b);
    const midIndex = Math.floor(sortedValues.length / 2);
    const median = sortedValues.length % 2 === 0
      ? (sortedValues[midIndex - 1] + sortedValues[midIndex]) / 2
      : sortedValues[midIndex];

    // Calculate standard deviation
    const squaredDifferences = numericValues.map(val => Math.pow(val - mean, 2));
    const variance = squaredDifferences.reduce((acc, val) => acc + val, 0) / numericValues.length;
    const stdDev = Math.sqrt(variance);

    // Create normalized values for the chart (between 0 and 1)
    const range = max - min;
    const normalizedValues = range === 0
      ? numericValues.map(() => 0.5) // If all values are the same, show middle bars
      : numericValues.map(val => (val - min) / range);

    // Enhanced color mapping based on value relative to mean and distribution
    const coloredValues = numericValues.map((originalValue, index) => {
      const normalizedValue = normalizedValues[index];
      const deviationFromMean = Math.abs(originalValue - mean);
      const relativeDeviation = stdDev > 0 ? deviationFromMean / stdDev : 0;

      // Determine color based on value position and deviation
      let color: string;
      let intensity: number;

      if (originalValue > mean + stdDev * 0.5) {
        // High values - Red spectrum
        intensity = Math.min(1, relativeDeviation / 2);
        color = `rgb(${Math.round(220 + intensity * 35)}, ${Math.round(38 - intensity * 20)}, ${Math.round(38 - intensity * 20)})`;
      } else if (originalValue < mean - stdDev * 0.5) {
        // Low values - Blue spectrum
        intensity = Math.min(1, relativeDeviation / 2);
        color = `rgb(${Math.round(59 - intensity * 20)}, ${Math.round(130 + intensity * 50)}, ${Math.round(246)})`;
      } else {
        // Medium values - Blue-green spectrum
        intensity = 0.6;
        color = `rgb(${Math.round(34)}, ${Math.round(197)}, ${Math.round(94)})`;
      }

      return {
        value: normalizedValue,
        color,
        intensity: intensity * 0.8 + 0.2 // Ensure minimum visibility
      };
    });

    return {
      min,
      max,
      mean,
      median,
      stdDev,
      values: normalizedValues,
      hasData: true,
      count: numericValues.length,
      coloredValues
    };
  }, [data, accessor]);

  // Format number to 2 decimal places
  const formatNumber = (num: number) => {
    return Math.round(num * 100) / 100;
  };

  // If there's no numeric data, show a placeholder
  if (!stats.hasData) {
    return (
      <div
        className="flex items-center justify-center text-xs text-muted-foreground bg-muted/20 rounded-sm"
        style={{ width, height }}
      >
        <span className="text-[10px] opacity-60">No data</span>
      </div>
    );
  }

  // Calculate how many bars to show (max 12 for better resolution)
  const barCount = Math.min(12, stats.coloredValues.length);
  const sampleInterval = Math.max(1, Math.floor(stats.coloredValues.length / barCount));
  const sampledValues = stats.coloredValues.filter((_, i) => i % sampleInterval === 0).slice(0, barCount);

  return (
    <div className="relative w-full">
      <TooltipProvider>
        <Tooltip delayDuration={200}>
          <TooltipTrigger asChild>
            <div className="absolute -top-1 -right-1 cursor-help z-10">
              <Info className="h-3 w-3 text-muted-foreground/50 hover:text-muted-foreground transition-colors" />
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" align="center" className="text-xs p-3 max-w-[240px] bg-popover/95 backdrop-blur-sm">
            <div className="space-y-2">
              <div className="font-semibold text-foreground">{accessor} Statistics</div>
              <div className="grid grid-cols-2 gap-x-3 gap-y-1.5 text-xs">
                <span className="text-muted-foreground">Count:</span>
                <span className="font-medium">{stats.count}</span>
                <span className="text-muted-foreground">Range:</span>
                <span className="font-medium">{formatNumber(stats.min)} - {formatNumber(stats.max)}</span>
                <span className="text-muted-foreground">Mean:</span>
                <span className="font-medium">{formatNumber(stats.mean)}</span>
                <span className="text-muted-foreground">Median:</span>
                <span className="font-medium">{formatNumber(stats.median)}</span>
                <span className="text-muted-foreground">Std Dev:</span>
                <span className="font-medium">{formatNumber(stats.stdDev)}</span>
              </div>
              <div className="pt-1 border-t border-border/50">
                <div className="flex items-center gap-2 text-[10px]">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-sm"></div>
                    <span>Low</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-emerald-500 rounded-sm"></div>
                    <span>Med</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-500 rounded-sm"></div>
                    <span>High</span>
                  </div>
                </div>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <div
        className="flex items-end justify-between gap-[0.5px] bg-muted/10 rounded-sm p-0.5"
        style={{ width, height }}
      >
        {sampledValues.map((coloredValue, index) => {
          const originalValue = coloredValue.value * (stats.max - stats.min) + stats.min;
          return (
            <div
              key={index}
              className={cn(
                "transition-all duration-200 rounded-[1px] w-full relative group cursor-pointer",
                "hover:scale-110 hover:z-10 hover:shadow-sm"
              )}
              style={{
                height: `${Math.max(3, coloredValue.value * (height - 4))}px`,
                backgroundColor: coloredValue.color,
                opacity: coloredValue.intensity
              }}
            >
              {/* Enhanced Tooltip on Hover */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-all duration-200 bg-popover/95 backdrop-blur-sm text-popover-foreground text-[10px] rounded-md px-2 py-1 whitespace-nowrap pointer-events-none z-50 shadow-lg border">
                <div className="font-medium">{formatNumber(originalValue)}</div>
                <div className="text-muted-foreground text-[9px]">
                  {originalValue > stats.mean ? 'Above avg' : originalValue < stats.mean ? 'Below avg' : 'Average'}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default ColumnBarChart;

