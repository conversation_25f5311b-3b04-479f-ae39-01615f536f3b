#!/usr/bin/env python3
"""
Test script to verify input handling and computer vision display fixes
"""

# Test 1: Interactive Input
print("=== Test 1: Interactive Input ===")
name = input("What's your name? ")
print(f"Hello, {name}!")

age = input("How old are you? ")
print(f"You are {age} years old.")

print("Input test completed!")

# Test 2: Computer Vision with MediaPipe
print("\n=== Test 2: Computer Vision Display ===")
import cv2
import numpy as np
import mediapipe as mp

# Create a test image
img = np.ones((480, 640, 3), dtype=np.uint8) * 255

# Add some text and shapes
cv2.putText(img, "Computer Vision Test", (150, 100), 
            cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
cv2.rectangle(img, (100, 150), (540, 350), (255, 0, 0), 3)
cv2.circle(img, (320, 250), 50, (0, 255, 0), -1)

# Display using cv2.imshow (should show in plots section)
cv2.imshow("Test Image", img)

print("Computer vision image displayed!")

# Test 3: MediaPipe Hand Detection Demo
print("\n=== Test 3: MediaPipe Demo ===")
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=True, max_num_hands=2)
mp_draw = mp.solutions.drawing_utils

# Create demo image with hand-like shapes
demo_img = np.ones((480, 640, 3), dtype=np.uint8) * 240
cv2.putText(demo_img, "MediaPipe Hand Detection Demo", (100, 50), 
            cv2.FONT_HERSHEY_SIMPLEX, 0.8, (50, 50, 50), 2)

# Add some circles to simulate hand landmarks
for i, (x, y) in enumerate([(200, 200), (250, 180), (300, 160), (350, 140), (400, 120)]):
    cv2.circle(demo_img, (x, y), 8, (0, 255, 255), -1)
    cv2.putText(demo_img, str(i), (x-5, y-15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

cv2.imshow("MediaPipe Demo", demo_img)

print("MediaPipe demo image displayed!")

# Test 4: Multiple frames (simulating video)
print("\n=== Test 4: Multiple Frames ===")
for i in range(3):
    frame = np.zeros((300, 400, 3), dtype=np.uint8)
    
    # Moving circle
    center_x = 50 + i * 100
    cv2.circle(frame, (center_x, 150), 30, (0, 255, 255), -1)
    
    # Frame number
    cv2.putText(frame, f"Frame {i+1}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    cv2.imshow(f"Frame {i+1}", frame)

print("Multiple frames displayed!")

print("\n=== All Tests Completed ===")
print("Check the output and plots sections to verify:")
print("1. Input prompts appear and can be answered")
print("2. Computer vision images appear in plots section")
print("3. Multiple frames are displayed properly")
