'use client'

import React from 'react';
import { Git<PERSON>ranch, Eye, Clock, ChevronRight, User, Edit3, Trash2, Plus, Activity } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { format } from 'date-fns';
import { Skeleton } from "@/components/ui/skeleton";
import { motion, AnimatePresence } from 'framer-motion';
import { VersionHistorySkeleton } from './VersionHistorySkeleton';

interface VersionHistoryProps {
  showVersionHistory: boolean;
  setShowVersionHistory: (show: boolean) => void;
  versionHistory: any[];
  currentDatasetId: string | null;
  loadVersionData: (datasetId: string, versionNumber: number) => void;
  isLoadingVersion?: boolean;
  datasetName?: string;
}

export const VersionHistory: React.FC<VersionHistoryProps> = ({
  showVersionHistory,
  setShowVersionHistory,
  versionHistory,
  currentDatasetId,
  loadVersionData,
  isLoadingVersion = false,
  datasetName = 'Dataset'
}) => {
  return (
    <AnimatePresence>
      {showVersionHistory && (
        <Dialog open={showVersionHistory} onOpenChange={setShowVersionHistory}>
          <DialogContent className="sm:max-w-[900px] max-h-[85vh] flex flex-col overflow-hidden">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3 }}
            >
              <DialogHeader className="border-b pb-4">
                <DialogTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <GitBranch className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex flex-col">
                    <span>Version History</span>
                    <span className="text-sm font-normal text-muted-foreground">{datasetName}</span>
                  </div>
                </DialogTitle>
                <DialogDescription className="text-base">
                  Track changes and evolution of your dataset over time
                </DialogDescription>
              </DialogHeader>
            </motion.div>
        {isLoadingVersion ? (
          <VersionHistorySkeleton />
        ) : (
        <div className="flex-1 overflow-hidden mt-6">
          <ScrollArea className="h-[65vh] pr-4">
            {versionHistory.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex flex-col items-center justify-center h-60 text-muted-foreground"
              >
                <div className="relative mb-6">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="p-4 bg-muted/30 rounded-full"
                  >
                    <GitBranch className="h-12 w-12 opacity-50" />
                  </motion.div>
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute -top-1 -right-1 h-4 w-4 bg-primary/20 rounded-full"
                  />
                </div>
                <h3 className="text-lg font-semibold mb-2">No Version History</h3>
                <p className="text-center max-w-sm">
                  Start making changes to your dataset to see version history appear here.
                </p>
              </motion.div>
            ) : (
              <div className="space-y-8">
                {versionHistory.map((version, index) => (
                  <motion.div
                    key={version.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="relative pl-12 before:absolute before:left-5 before:top-8 before:h-full before:w-[2px] before:bg-gradient-to-b before:from-primary/50 before:to-muted last:before:h-0"
                  >
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="absolute left-0 top-2 h-10 w-10 rounded-full border-2 border-primary bg-background flex items-center justify-center text-sm font-bold text-primary shadow-lg"
                    >
                      {version.versionNumber}
                      <motion.div
                        animate={{ scale: [1, 1.3, 1] }}
                        transition={{ duration: 2, repeat: Infinity, delay: index * 0.2 }}
                        className="absolute inset-0 rounded-full border-2 border-primary/30"
                      />
                    </motion.div>
                    <motion.div
                      whileHover={{ y: -2, scale: 1.02 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <Card className="overflow-hidden transition-all hover:shadow-lg border-l-4 border-l-primary/20 hover:border-l-primary/60 bg-gradient-to-r from-background to-muted/10">
                        <CardHeader className="pb-2 pt-4 px-5">
                          <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                              <div className="flex items-center gap-3">
                                <Badge variant="outline" className="font-semibold bg-primary/10 border-primary/30 text-primary">
                                  v{version.versionNumber}
                                </Badge>
                                <div className="flex items-center gap-1">
                                  <User className="h-3 w-3 text-muted-foreground" />
                                  <span className="text-sm font-medium">
                                    {version.user?.name || 'Admin'}
                                  </span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                                <Clock className="h-3.5 w-3.5" />
                                {format(new Date(version.createdAt), 'MMM d, yyyy HH:mm')}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary" className="bg-muted/50">
                                <Activity className="h-3 w-3 mr-1" />
                                {version.changes.length} {version.changes.length === 1 ? 'change' : 'changes'}
                              </Badge>
                              <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => loadVersionData(currentDatasetId!, version.versionNumber)}
                                  className="h-8 px-3 hover:bg-primary/10 hover:text-primary"
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  View
                                </Button>
                              </motion.div>
                            </div>
                          </div>
                        </CardHeader>
                      <CardContent className="px-5 pt-0">
                        <div className="mt-3 space-y-3">
                          {version.changes.slice(0, 3).map((change: any, i: number) => (
                            <motion.div
                              key={i}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: i * 0.1 }}
                              className="flex items-start gap-3 text-sm p-3 rounded-lg bg-gradient-to-r from-muted/30 to-muted/10 border border-muted/20"
                            >
                              {change.type === 'edit' ? (
                                <>
                                  <div className="p-1 bg-blue-500/10 rounded-md">
                                    <Edit3 className="h-3 w-3 text-blue-600" />
                                  </div>
                                  <div className="flex flex-col w-full">
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium text-foreground">
                                        Edited {change.column} (Row {(change.row || 0) + 1})
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-2 mt-2">
                                      <span className="line-through text-red-500/80 max-w-[180px] truncate bg-red-50/50 dark:bg-red-950/20 px-2 py-1 rounded">
                                        {String(change.oldValue || 'empty')}
                                      </span>
                                      <ChevronRight className="h-3.5 w-3.5 text-muted-foreground" />
                                      <span className="text-green-600 max-w-[180px] truncate font-medium bg-green-50/50 dark:bg-green-950/20 px-2 py-1 rounded">
                                        {String(change.newValue || 'empty')}
                                      </span>
                                    </div>
                                  </div>
                                </>
                              ) : change.type === 'add' ? (
                                <>
                                  <div className="p-1 bg-green-500/10 rounded-md">
                                    <Plus className="h-3 w-3 text-green-600" />
                                  </div>
                                  <div className="flex flex-col w-full">
                                    <span className="font-medium text-foreground">Added new row</span>
                                    <span className="text-muted-foreground text-xs mt-1">Row {(change.row || 0) + 1}</span>
                                  </div>
                                </>
                              ) : (
                                <>
                                  <div className="p-1 bg-red-500/10 rounded-md">
                                    <Trash2 className="h-3 w-3 text-red-600" />
                                  </div>
                                  <div className="flex flex-col w-full">
                                    <span className="font-medium text-foreground">Deleted row</span>
                                    <span className="text-muted-foreground text-xs mt-1">Row {(change.row || 0) + 1}</span>
                                  </div>
                                </>
                              )}
                            </motion.div>
                          ))}
                          {version.changes.length > 3 && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.3 }}
                              className="text-center pt-3"
                            >
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => loadVersionData(currentDatasetId!, version.versionNumber)}
                                className="text-xs text-muted-foreground hover:text-foreground hover:bg-muted/50"
                              >
                                <Plus className="h-3 w-3 mr-1" />
                                View {version.changes.length - 3} more changes
                              </Button>
                            </motion.div>
                          )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </motion.div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
        )}
          </DialogContent>
        </Dialog>
      )}
    </AnimatePresence>
  );
};
