'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Download, Upload, Copy, Check, Code, FileJson } from "lucide-react"
import { toast } from "sonner"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"
import { Dataset } from '@/types/index'

export interface NotebookExport {
  version: string;
  timestamp: string;
  title: string;
  cells: {
    id: string;
    content: string;
    language: string;
    result?: {
      data: any[];
      output?: string;
      plots?: string[];
      error?: string;
      errorDetails?: any;
      executionTime?: number;
    };
    showGraphicWalker?: boolean;
    isSuccess?: boolean;
    viewMode?: 'table' | 'chart' | 'output' | 'plots' | 'graphicwalker';
  }[];
  datasets?: {
    id: string;
    name: string;
    columns?: string[];
    headers?: string[];
    fileType?: string;
  }[];
  savedCharts: {
    id: string;
    title: string;
    description: string;
    chartType: 'line' | 'bar' | 'pie' | 'area';
    data: any[];
    config: any;
    createdAt: string;
    size?: 'small' | 'medium' | 'large' | 'full';
  }[];
}

interface NotebookExporterProps {
  cells: any[];
  savedCharts: any[];
  selectedDatasets: Dataset[];
  onImport: (importedNotebook: NotebookExport) => void;
}

export function NotebookExporter({
  cells,
  savedCharts,
  selectedDatasets,
  onImport
}: NotebookExporterProps) {
  const [copied, setCopied] = useState(false)
  const [importJson, setImportJson] = useState('')
  const [importError, setImportError] = useState('')

  // Create exportable notebook config
  const notebookExport: NotebookExport = {
    version: '1.0',
    timestamp: new Date().toISOString(),
    title: 'SQL Notebook Export',
    cells: cells.map(cell => ({
      id: cell.id,
      content: cell.content,
      language: cell.language,
      result: cell.result,
      showGraphicWalker: cell.showGraphicWalker,
      isSuccess: cell.isSuccess,
      viewMode: cell.viewMode
    })),
    // Export all selected datasets
    datasets: selectedDatasets.map(dataset => ({
      id: dataset.id,
      name: dataset.name,
      columns: Array.isArray(dataset.columns)
        ? dataset.columns.map(col => typeof col === 'string' ? col : col.name)
        : undefined,
      headers: dataset.headers,
      fileType: dataset.fileType
    })),
    savedCharts: savedCharts.map(chart => ({
      ...chart,
      createdAt: chart.createdAt instanceof Date
        ? chart.createdAt.toISOString()
        : typeof chart.createdAt === 'string'
          ? chart.createdAt
          : new Date().toISOString() // Fallback to current date if createdAt is invalid
    }))
  }

  // Convert to JSON string
  const jsonConfig = JSON.stringify(notebookExport, null, 2)

  // Copy to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonConfig)
    setCopied(true)
    toast.success('Notebook copied to clipboard')
    setTimeout(() => setCopied(false), 2000)
  }

  // Download as JSON file
  const downloadJson = () => {
    const blob = new Blob([jsonConfig], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `sql-notebook-${new Date().getTime()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Notebook downloaded as JSON')
  }

  // Handle import
  const handleImport = () => {
    try {
      setImportError('')
      if (!importJson.trim()) {
        setImportError('Please paste a valid JSON notebook')
        return
      }

      const parsed = JSON.parse(importJson)

      // Basic validation
      if (!parsed.cells || !Array.isArray(parsed.cells)) {
        setImportError('Invalid notebook format: missing cells array')
        return
      }

      // Convert ISO dates back to Date objects
      if (parsed.savedCharts) {
        parsed.savedCharts = parsed.savedCharts.map((chart: any) => ({
          ...chart,
          createdAt: new Date(chart.createdAt)
        }));
      }

      // Make sure all required cell properties are present
      parsed.cells = parsed.cells.map((cell: any) => ({
        ...cell,
        content: cell.content || '',
        language: cell.language || 'sql',
        isSuccess: cell.isSuccess || false,
        showGraphicWalker: cell.showGraphicWalker || false,
        viewMode: cell.viewMode || 'table'
      }));

      // Make sure all required chart properties are present
      if (parsed.savedCharts) {
        parsed.savedCharts = parsed.savedCharts.map((chart: any) => ({
          ...chart,
          chartType: chart.chartType || 'bar',
          title: chart.title || 'Untitled Chart',
          description: chart.description || '',
          size: chart.size || 'medium',
          config: chart.config || {}
        }));
      }

      // Import the notebook
      onImport(parsed)
      toast.success('Notebook imported successfully')
      setImportJson('')
    } catch (error) {
      console.error('Import error:', error)
      setImportError('Failed to parse JSON notebook configuration')
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="ml-2"
          data-export-notebook
        >
          <FileJson className="h-4 w-4 mr-2" />
          <span>Export/Import Notebook</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>SQL Notebook Import/Export</DialogTitle>
          <DialogDescription>
            Save your entire notebook as JSON or import a previously saved notebook
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="export">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="export">Export</TabsTrigger>
            <TabsTrigger value="import">Import</TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="space-y-4">
            <div className="text-sm text-muted-foreground mb-2">
              This JSON contains your entire notebook, including cells, code, results, and dashboard charts.
            </div>

            <ScrollArea className="h-[300px] w-full rounded-md border">
              <pre className="p-4 text-sm font-mono">{jsonConfig}</pre>
            </ScrollArea>

            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={copyToClipboard}
              >
                {copied ? <Check className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
                {copied ? 'Copied' : 'Copy'}
              </Button>

              <Button
                onClick={downloadJson}
                data-export-notebook
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="import" className="space-y-4">
            <div className="text-sm text-muted-foreground mb-2">
              Paste a previously exported SQL Notebook JSON to restore your cells, data, and charts
            </div>

            <Textarea
              placeholder="Paste notebook JSON here..."
              className="font-mono text-sm h-[300px]"
              value={importJson}
              onChange={(e) => setImportJson(e.target.value)}
            />

            {importError && (
              <div className="text-sm text-red-500">{importError}</div>
            )}

            <div className="flex justify-end">
              <Button
                onClick={handleImport}
                data-import-notebook
              >
                <Upload className="h-4 w-4 mr-1" />
                Import Notebook
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}