#!/usr/bin/env python3
"""
Test simple Jupyter-like functionality
"""

# Test 1: Simple output
print("Hello, Jupyter-like output!")
print("This is simple and clean.")

# Test 2: Variables and expressions
x = 10
y = 20
print(f"x = {x}, y = {y}")
print(f"x + y = {x + y}")

# The last expression will be displayed
x + y

# Test 3: Lists and data
numbers = [1, 2, 3, 4, 5]
print("Numbers:", numbers)

squares = [n**2 for n in numbers]
print("Squares:", squares)

# Display the result
squares

# Test 4: Input simulation (works without hanging)
name = input("What's your name? ")
print(f"Hello, {name}!")

age = input("How old are you? ")
print(f"You are {age} years old!")

# Test 5: Simple calculations
total = sum(numbers)
print(f"Sum of numbers: {total}")

# Show the result
total * 2
